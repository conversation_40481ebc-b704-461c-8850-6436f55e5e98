import React, { useState, useRef } from 'react';
import { Download, Eye, Palette, Layout, FileText, Columns, Clock, Triangle, Target } from 'lucide-react';

const PPTGenerator = () => {
  const [outline, setOutline] = useState(`# My Presentation Title
## Subtitle: Making great presentations easy

# Traditional vs Digital Museum Comparison
| Traditional Museum | Digital Language Museum |
| :-------------------------------------- | :---------------------- |
| Static displays | Interactive experiences |
| Limited access | Global reach |
| Observation only | Community participation |
| Fixed narratives | Evolving stories |

# About Our Solution
- Faster processing capabilities
- Significant cost savings
- Better accuracy rates
- Improved user experience

# Benefits vs Challenges
Benefits:
- Reduced operational costs
- Faster time to market
- Enhanced productivity
- Better customer satisfaction

Challenges:
- Initial learning curve
- System integration requirements
- Training investment needed
- Change management process

# Implementation Timeline
## Phase 1: Planning
Initial assessment and strategy development

## Phase 2: Development  
System setup and configuration

## Phase 3: Testing
Quality assurance and user acceptance

## Phase 4: Launch
Full deployment and go-live

# Key Features
## Feature 1: Advanced Analytics
Real-time insights and reporting

## Feature 2: Automated Workflows
Streamlined processes and efficiency

## Feature 3: Integration Capabilities
Seamless connectivity with existing systems`);

  const [theme, setTheme] = useState({
    primary: '#0D47A1',
    accent: '#1976D2',
    accentLight: '#E3F2FD',
    accentBorder: '#64B5F6',
    textDark: '#333333',
    textLight: '#757575'
  });

  const [selectedTemplate, setSelectedTemplate] = useState('auto');
  const [slides, setSlides] = useState([]);
  const [previewMode, setPreviewMode] = useState(true);

  const slideRef = useRef(null);

  // Intelligent content parser with table support
  const parseOutline = (text) => {
    const lines = text.split('\n').filter(line => line.trim());
    const slides = [];
    let currentSlide = null;
    let inTable = false;
    let tableHeaders = [];
    let tableRows = [];

    const processTable = () => {
      if (tableHeaders.length > 0 && tableRows.length > 0) {
        // Create a table slide
        currentSlide.content.push({
          type: 'table',
          headers: tableHeaders,
          rows: tableRows,
          isComparison: tableHeaders.length === 2 // Two-column tables are treated as comparisons
        });
      }
      tableHeaders = [];
      tableRows = [];
      inTable = false;
    };

    lines.forEach((line, index) => {
      const trimmed = line.trim();
      
      // Check if this is a table row
      if (trimmed.startsWith('|') && trimmed.endsWith('|')) {
        if (!inTable) {
          inTable = true;
          // Parse headers
          tableHeaders = trimmed.split('|')
            .map(cell => cell.trim())
            .filter(cell => cell !== '');
        } else {
          // Check if this is a separator line (contains dashes and colons)
          if (trimmed.includes('-') || trimmed.includes(':')) {
            // Skip separator line
            return;
          }
          // Parse data row
          const row = trimmed.split('|')
            .map(cell => cell.trim())
            .filter(cell => cell !== '');
          if (row.length > 0) {
            tableRows.push(row);
          }
        }
        return;
      } else if (inTable) {
        // End of table, process it
        processTable();
      }
      
      if (trimmed.startsWith('# ')) {
        // Main title - create new slide
        if (currentSlide) {
          if (inTable) processTable();
          slides.push(currentSlide);
        }
        currentSlide = {
          type: 'title',
          title: trimmed.substring(2),
          subtitle: '',
          content: [],
          rawContent: trimmed
        };
      } else if (trimmed.startsWith('## ')) {
        // Subtitle or section
        if (currentSlide && currentSlide.type === 'title' && !currentSlide.subtitle) {
          currentSlide.subtitle = trimmed.substring(3);
        } else {
          if (currentSlide) {
            if (inTable) processTable();
            slides.push(currentSlide);
          }
          currentSlide = {
            type: 'content',
            title: trimmed.substring(3),
            content: [],
            rawContent: trimmed
          };
        }
      } else if (trimmed.startsWith('- ')) {
        // Bullet point
        if (currentSlide) {
          currentSlide.content.push({
            type: 'bullet',
            text: trimmed.substring(2)
          });
        }
      } else if (trimmed && currentSlide) {
        // Regular text
        currentSlide.content.push({
          type: 'text',
          text: trimmed
        });
      }
    });

    // Process any remaining table
    if (inTable) processTable();
    if (currentSlide) slides.push(currentSlide);
    return slides;
  };

  // Intelligent template selection with table support
  const determineSlideTemplate = (slide) => {
    if (selectedTemplate !== 'auto') return selectedTemplate;
    
    if (slide.type === 'title') return 'cover';
    
    // Check for tables first
    const hasTable = slide.content.some(item => item.type === 'table');
    if (hasTable) {
      const table = slide.content.find(item => item.type === 'table');
      if (table.isComparison) {
        return 'table-comparison';
      }
      return 'table';
    }
    
    const content = slide.content.map(c => c.text || '').join(' ').toLowerCase();
    const title = slide.title.toLowerCase();
    
    // Check for comparison patterns
    if (content.includes('vs') || content.includes('versus') || 
        content.includes('benefits') && content.includes('challenges') ||
        content.includes('before') && content.includes('after') ||
        content.includes('advantages') && content.includes('disadvantages')) {
      return 'columns';
    }
    
    // Check for timeline/process patterns
    if (title.includes('timeline') || title.includes('process') || title.includes('steps') ||
        content.includes('phase') || content.includes('step')) {
      return 'timeline';
    }
    
    // Check for feature lists
    if (title.includes('features') || title.includes('capabilities') || 
        slide.content.length > 6) {
      return 'pyramid';
    }
    
    return 'content';
  };

  // Smart content separation for columns
  const separateContentForColumns = (content) => {
    const text = content.map(c => c.text).join(' ');
    
    // Look for comparison keywords
    const comparisonPatterns = [
      { keywords: ['benefits:', 'challenges:'], split: true },
      { keywords: ['advantages:', 'disadvantages:'], split: true },
      { keywords: ['before:', 'after:'], split: true },
      { keywords: ['pros:', 'cons:'], split: true }
    ];
    
    for (let pattern of comparisonPatterns) {
      const [key1, key2] = pattern.keywords;
      if (text.toLowerCase().includes(key1) && text.toLowerCase().includes(key2)) {
        const col1 = [];
        const col2 = [];
        let currentCol = col1;
        
        content.forEach(item => {
          const itemText = item.text.toLowerCase();
          if (itemText.includes(key1.replace(':', ''))) {
            currentCol = col1;
            col1.push({ ...item, text: item.text.replace(/^(benefits?|advantages?|before|pros?):\s*/i, '') });
          } else if (itemText.includes(key2.replace(':', ''))) {
            currentCol = col2;
            col2.push({ ...item, text: item.text.replace(/^(challenges?|disadvantages?|after|cons?):\s*/i, '') });
          } else {
            currentCol.push(item);
          }
        });
        
        return {
          col1: { title: key1.replace(':', '').charAt(0).toUpperCase() + key1.slice(1, -1), content: col1 },
          col2: { title: key2.replace(':', '').charAt(0).toUpperCase() + key2.slice(1, -1), content: col2 }
        };
      }
    }
    
    // Default: split content in half
    const mid = Math.ceil(content.length / 2);
    return {
      col1: { title: 'Key Points', content: content.slice(0, mid) },
      col2: { title: 'Additional Details', content: content.slice(mid) }
    };
  };

  // Generate slides from outline
  const generateSlides = () => {
    const parsedSlides = parseOutline(outline);
    const generatedSlides = parsedSlides.map((slide, index) => ({
      ...slide,
      id: index,
      template: determineSlideTemplate(slide),
      separatedContent: slide.content.length > 3 ? separateContentForColumns(slide.content) : null
    }));
    setSlides(generatedSlides);
  };

  // Export as HTML
  const exportAsHTML = () => {
    try {
      const htmlContent = generateHTMLExport();
      if (!htmlContent || htmlContent.length < 100) {
        alert('❌ 导出失败：生成的HTML内容为空或过短');
        return;
      }
      
      const blob = new Blob([htmlContent], { type: 'text/html' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = 'presentation.html';
      a.click();
      URL.revokeObjectURL(url);
      
      alert('✅ HTML文件导出成功！文件已下载到您的设备。');
    } catch (error) {
      console.error('Export error:', error);
      alert(`❌ 导出失败：${error.message}`);
    }
  };

  const generateHTMLExport = () => {
    const css = `
    <style>
      :root {
        --color-primary: ${theme.primary};
        --color-accent: ${theme.accent};
        --color-accent-light: ${theme.accentLight};
        --color-accent-border: ${theme.accentBorder};
        --color-text-dark: ${theme.textDark};
        --color-text-light: ${theme.textLight};
        --font-family-sans: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif;
      }

      body {
        background-color: #F0F2F5;
        font-family: var(--font-family-sans);
        color: var(--color-text-dark);
        margin: 0;
        padding: 20px;
      }

      .slide {
        background-color: #ffffff;
        border: 1px solid #E0E0E0;
        box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
        border-radius: 12px;
        width: 960px;
        height: 540px;
        margin: 25px auto;
        padding: 40px 60px;
        box-sizing: border-box;
        display: flex;
        flex-direction: column;
        overflow: hidden;
      }

      .slide h1, .slide h2, .slide h3, .slide h4 {
        color: var(--color-primary);
        font-weight: 700;
        margin: 0;
      }
      .slide h1 { font-size: 48px; }
      .slide h2 { 
        font-size: 34px; 
        padding-bottom: 15px;
        margin-bottom: 25px;
        border-bottom: 3px solid var(--color-accent-border);
      }
      .slide .slide-title-no-border { border-bottom: none; margin-bottom: 15px; }
      .slide h3 { font-size: 24px; margin-bottom: 10px; }
      .slide h4 { font-size: 20px; font-weight: 600; margin-bottom: 5px; }
      
      .slide p, .slide li {
        font-size: 16px;
        line-height: 1.7;
        color: var(--color-text-dark);
      }
      .slide .subtitle {
        font-size: 24px;
        font-weight: 300;
        color: var(--color-text-light);
        text-align: center;
        margin-top: 10px;
      }
      .slide .text-sm {
        font-size: 14px;
        color: var(--color-text-light);
      }

      .content-area { flex-grow: 1; display: flex; flex-direction: column; }
      .columns { display: flex; flex-grow: 1; width: 100%; gap: 40px; }
      .columns .col { flex: 1; }
      .layout-center { justify-content: center; align-items: center; text-align: center; }

      .timeline { position: relative; display: flex; justify-content: space-around; width: 100%; margin-top: 20px; }
      .timeline::before { content: ''; position: absolute; top: 20px; left: 10%; right: 10%; height: 2px; background-color: var(--color-accent-border); z-index: 1; }
      .timeline-item { display: flex; flex-direction: column; align-items: center; flex: 1; text-align: center; position: relative; z-index: 2; }
      .timeline-marker { width: 40px; height: 40px; border-radius: 50%; background-color: var(--color-accent-light); border: 2px solid var(--color-accent-border); display: flex; justify-content: center; align-items: center; font-size: 18px; font-weight: 600; color: var(--color-accent); margin-bottom: 15px; }

      .process-list { display: flex; flex-direction: column; align-items: center; gap: 15px; }
      .process-item { display: flex; align-items: center; gap: 25px; width: 80%; }
      .process-step { flex-shrink: 0; width: 100px; height: 80px; background-color: var(--color-accent-light); display: flex; justify-content: center; align-items: center; font-size: 32px; font-weight: 600; color: var(--color-primary); }
      .process-step.step-1 { clip-path: polygon(30% 0, 70% 0, 85% 100%, 15% 100%); }
      .process-step.step-2 { clip-path: polygon(20% 0, 80% 0, 95% 100%, 5% 100%); }
      .process-step.step-3 { clip-path: polygon(10% 0, 90% 0, 100% 100%, 0% 100%); }

      .slide ul { list-style-type: none; padding-left: 0; }
      .slide li { padding: 5px 0; }
      .slide li:before { content: "•"; color: var(--color-accent); font-weight: bold; display: inline-block; width: 1em; }

      /* Table styles */
      .comparison-table { width: 100%; border-collapse: separate; border-spacing: 20px 0; margin-top: 20px; }
      .comparison-table th { 
        background-color: var(--color-accent-light); 
        color: var(--color-primary); 
        padding: 15px 20px; 
        text-align: center; 
        font-weight: 600; 
        border-radius: 8px 8px 0 0;
        font-size: 18px;
      }
      .comparison-table td { 
        background-color: #FAFAFA; 
        padding: 12px 20px; 
        border-left: 3px solid var(--color-accent-border); 
        font-size: 15px;
        line-height: 1.6;
      }
      .comparison-table tr:last-child td { border-radius: 0 0 8px 8px; }

      .standard-table { width: 100%; border-collapse: collapse; margin-top: 20px; }
      .standard-table th { 
        background-color: var(--color-primary); 
        color: white; 
        padding: 12px 15px; 
        text-align: left; 
        font-weight: 600; 
      }
      .standard-table td { 
        padding: 10px 15px; 
        border-bottom: 1px solid #E0E0E0; 
      }
      .standard-table tr:nth-child(even) { background-color: #F9F9F9; }
    </style>
  `;

    const slidesHTML = slides.map(slide => {
      switch (slide.template) {
        case 'cover':
          return `
            <div class="slide layout-center">
              <h1>${slide.title}</h1>
              ${slide.subtitle ? `<p class="subtitle">${slide.subtitle}</p>` : ''}
            </div>
          `;
        
        case 'table-comparison':
          const table = slide.content.find(item => item.type === 'table');
          if (table) {
            return `
              <div class="slide">
                <h2>${slide.title}</h2>
                <table class="comparison-table">
                  <tr>
                    ${table.headers.map(header => `<th>${header}</th>`).join('')}
                  </tr>
                  ${table.rows.map(row => `
                    <tr>
                      ${row.map(cell => `<td>${cell}</td>`).join('')}
                    </tr>
                  `).join('')}
                </table>
              </div>
            `;
          }
          return '<div class="slide"><h2>Error: Table not found</h2></div>';
        
        case 'table':
          const standardTable = slide.content.find(item => item.type === 'table');
          if (standardTable) {
            return `
              <div class="slide">
                <h2>${slide.title}</h2>
                <table class="standard-table">
                  <thead>
                    <tr>
                      ${standardTable.headers.map(header => `<th>${header}</th>`).join('')}
                    </tr>
                  </thead>
                  <tbody>
                    ${standardTable.rows.map(row => `
                      <tr>
                        ${row.map(cell => `<td>${cell}</td>`).join('')}
                      </tr>
                    `).join('')}
                  </tbody>
                </table>
              </div>
            `;
          }
          return '<div class="slide"><h2>Error: Table not found</h2></div>';
        
        case 'columns':
          const separated = slide.separatedContent || separateContentForColumns(slide.content.filter(item => item.type !== 'table'));
          return `
            <div class="slide">
              <h2>${slide.title}</h2>
              <div class="columns">
                <div class="col">
                  <h3>${separated.col1.title}</h3>
                  <ul>
                    ${separated.col1.content.map(item => `<li>${item.text}</li>`).join('')}
                  </ul>
                </div>
                <div class="col">
                  <h3>${separated.col2.title}</h3>
                  <ul>
                    ${separated.col2.content.map(item => `<li>${item.text}</li>`).join('')}
                  </ul>
                </div>
              </div>
            </div>
          `;
        
        case 'timeline':
          const timelineContent = slide.content.filter(item => item.type !== 'table');
          return `
            <div class="slide">
              <h2 class="slide-title-no-border">${slide.title}</h2>
              <div class="content-area" style="justify-content: center;">
                <div class="timeline">
                  ${timelineContent.slice(0, 4).map((item, index) => `
                    <div class="timeline-item">
                      <div class="timeline-marker">${index + 1}</div>
                      <h4>${item.type === 'bullet' ? item.text.split(':')[0] : `Step ${index + 1}`}</h4>
                      <p class="text-sm">${item.type === 'bullet' && item.text.includes(':') ? item.text.split(':')[1] : item.text}</p>
                    </div>
                  `).join('')}
                </div>
              </div>
            </div>
          `;
        
        case 'pyramid':
          const pyramidContent = slide.content.filter(item => item.type !== 'table');
          return `
            <div class="slide">
              <h2 class="slide-title-no-border">${slide.title}</h2>
              <div class="content-area" style="justify-content: center;">
                <div class="process-list">
                  ${pyramidContent.slice(0, 3).map((item, index) => `
                    <div class="process-item">
                      <div class="process-step step-${index + 1}">${index + 1}</div>
                      <div class="process-content">
                        <h4>${item.type === 'bullet' ? item.text.split(':')[0] : `Point ${index + 1}`}</h4>
                        <p class="text-sm">${item.type === 'bullet' && item.text.includes(':') ? item.text.split(':')[1] : item.text}</p>
                      </div>
                    </div>
                  `).join('')}
                </div>
              </div>
            </div>
          `;
        
        default:
          const contentItems = slide.content.filter(item => item.type !== 'table');
          const tableItems = slide.content.filter(item => item.type === 'table');
          let slideHTML = `
            <div class="slide">
              <h2>${slide.title}</h2>`;
          
          // Add regular content
          if (contentItems.length > 0) {
            slideHTML += contentItems.map(item => 
              item.type === 'bullet' ? `<li>${item.text}</li>` : `<p>${item.text}</p>`
            ).join('');
          }
          
          // Add tables
          if (tableItems.length > 0) {
            slideHTML += tableItems.map(table => `
              <table class="standard-table">
                <thead>
                  <tr>
                    ${table.headers.map(header => `<th>${header}</th>`).join('')}
                  </tr>
                </thead>
                <tbody>
                  ${table.rows.map(row => `
                    <tr>
                      ${row.map(cell => `<td>${cell}</td>`).join('')}
                    </tr>
                  `).join('')}
                </tbody>
              </table>
            `).join('');
          }
          
          slideHTML += `</div>`;
          return slideHTML;
      }
    }).join('');

    return `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Generated Presentation</title>
  ${css}
</head>
<body>
  ${slidesHTML}
</body>
</html>`;
  };

  // Export as PPTX-compatible JSON
  const exportAsPPTX = () => {
    const pptxData = {
      title: "Generated Presentation",
      theme: theme,
      slides: slides.map(slide => ({
        id: slide.id,
        type: slide.template,
        title: slide.title,
        subtitle: slide.subtitle || '',
        content: slide.content,
        separatedContent: slide.separatedContent
      }))
    };
    
    const jsonString = JSON.stringify(pptxData, null, 2);
    const blob = new Blob([jsonString], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'presentation-data.json';
    a.click();
    URL.revokeObjectURL(url);
    
    // Show instructions
    alert(`
📋 PPTX导出说明：

1. ✅ 已下载 presentation-data.json 文件
2. 📄 请使用导出的HTML文件作为参考
3. 🔄 复制HTML内容到PowerPoint：
   - 在浏览器中打开导出的HTML文件
   - 选择并复制每个幻灯片的内容
   - 粘贴到PowerPoint中，保持格式

4. 🎨 或使用在线转换工具：
   - 将HTML文件上传到在线HTML转PPT工具
   - 下载生成的PPTX文件

💡 提示：JSON文件包含了完整的演示数据，可以用于其他工具进一步处理。
    `);
  };

  React.useEffect(() => {
    try {
      generateSlides();
    } catch (error) {
      console.error('Error generating slides:', error);
    }
  }, [outline, selectedTemplate, theme]);

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto">
        <div className="bg-white rounded-lg shadow-lg p-6 mb-6">
          <h1 className="text-3xl font-bold text-gray-800 mb-4 flex items-center gap-3">
            <FileText className="text-blue-600" />
            Full-Featured PPT Generator
          </h1>
          
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Input Panel */}
            <div className="lg:col-span-1 space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Presentation Outline
                </label>
                <textarea
                  value={outline}
                  onChange={(e) => setOutline(e.target.value)}
                  className="w-full h-64 p-3 border border-gray-300 rounded-md font-mono text-sm"
                  placeholder="Enter your presentation outline..."
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  <Layout className="inline w-4 h-4 mr-1" />
                  Template Selection
                </label>
                <select
                  value={selectedTemplate}
                  onChange={(e) => setSelectedTemplate(e.target.value)}
                  className="w-full p-2 border border-gray-300 rounded-md"
                >
                  <option value="auto">🤖 Auto-Select</option>
                  <option value="cover">📄 Cover Slide</option>
                  <option value="content">📝 Standard Content</option>
                  <option value="columns">📊 Two Columns</option>
                  <option value="table">📋 Table</option>
                  <option value="table-comparison">⚖️ Comparison Table</option>
                  <option value="timeline">⏱️ Timeline</option>
                  <option value="pyramid">🔺 Pyramid/Process</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  <Palette className="inline w-4 h-4 mr-1" />
                  Color Theme
                </label>
                <div className="grid grid-cols-2 gap-2">
                  <div>
                    <label className="text-xs text-gray-600">Primary</label>
                    <input
                      type="color"
                      value={theme.primary}
                      onChange={(e) => setTheme({...theme, primary: e.target.value})}
                      className="w-full h-8 rounded border"
                    />
                  </div>
                  <div>
                    <label className="text-xs text-gray-600">Accent</label>
                    <input
                      type="color"
                      value={theme.accent}
                      onChange={(e) => setTheme({...theme, accent: e.target.value})}
                      className="w-full h-8 rounded border"
                    />
                  </div>
                </div>
              </div>

              <div className="flex gap-2">
                <button
                  onClick={() => setPreviewMode(!previewMode)}
                  className="flex-1 bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors flex items-center justify-center gap-2"
                >
                  <Eye className="w-4 h-4" />
                  {previewMode ? 'Hide' : 'Show'} Preview
                </button>
              </div>

              <div className="border-t pt-4 space-y-2">
                <button
                  onClick={exportAsHTML}
                  className="w-full bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 transition-colors flex items-center justify-center gap-2"
                >
                  <Download className="w-4 h-4" />
                  Export as HTML
                </button>
                <button
                  onClick={exportAsPPTX}
                  className="w-full bg-orange-600 text-white px-4 py-2 rounded-md hover:bg-orange-700 transition-colors flex items-center justify-center gap-2"
                >
                  <FileText className="w-4 h-4" />
                  Export as PPTX Data
                </button>
                <button
                  onClick={() => console.log('Slides data:', slides)}
                  className="w-full bg-gray-600 text-white px-4 py-1 rounded-md hover:bg-gray-700 transition-colors text-sm"
                >
                  🔍 Debug Info (Check Console)
                </button>
              </div>

              <div className="text-xs text-gray-600 bg-blue-50 p-3 rounded">
                <strong>智能功能:</strong> 系统会自动识别对比内容、时间线和流程，并选择最适合的布局模板。
                <div className="mt-2 flex gap-4 text-xs">
                  <span>📊 解析到 {slides.length} 张幻灯片</span>
                  <span>📋 包含 {slides.filter(s => s.content.some(c => c.type === 'table')).length} 个表格</span>
                </div>
              </div>
            </div>

            {/* Preview Panel */}
            {previewMode && (
              <div className="lg:col-span-2">
                <div className="bg-gray-100 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-4">
                    <h2 className="text-lg font-semibold text-gray-800">Live Preview</h2>
                    <span className="text-sm text-gray-600">{slides.length} slides generated</span>
                  </div>
                  
                  <div className="space-y-4 max-h-96 overflow-y-auto">
                    {slides.map((slide, index) => (
                      <div key={slide.id} className="bg-white rounded-lg shadow-sm border">
                        <div 
                          className="slide-preview"
                          style={{
                            width: '100%',
                            aspectRatio: '16/9',
                            padding: '20px',
                            fontSize: '12px',
                            '--color-primary': theme.primary,
                            '--color-accent': theme.accent,
                            '--color-accent-light': theme.accentLight,
                            '--color-accent-border': theme.accentBorder,
                            '--color-text-dark': theme.textDark,
                            '--color-text-light': theme.textLight
                          }}
                        >
                          <div className="flex items-center justify-between mb-2">
                            <span className="text-xs font-medium px-2 py-1 bg-blue-100 text-blue-800 rounded">
                              {slide.template === 'table-comparison' ? '⚖️ 对比表格' : 
                               slide.template === 'table' ? '📋 标准表格' :
                               slide.template.charAt(0).toUpperCase() + slide.template.slice(1)}
                            </span>
                            <div className="flex items-center gap-1">
                              <span className="text-xs text-gray-500">Slide {index + 1}</span>
                              {slide.content.some(c => c.type === 'table') && (
                                <span className="text-xs bg-green-100 text-green-700 px-1 rounded">📊</span>
                              )}
                            </div>
                          </div>
                          
                          {slide.template === 'cover' ? (
                            <div className="text-center py-8">
                              <h1 className="text-2xl font-bold mb-2" style={{color: theme.primary}}>
                                {slide.title}
                              </h1>
                              {slide.subtitle && (
                                <p className="text-lg" style={{color: theme.textLight}}>
                                  {slide.subtitle}
                                </p>
                              )}
                            </div>
                          ) : slide.template === 'table-comparison' ? (
                            <div>
                              <h2 className="text-lg font-bold mb-3" style={{color: theme.primary}}>
                                {slide.title}
                              </h2>
                              {(() => {
                                const table = slide.content.find(item => item.type === 'table');
                                return table ? (
                                  <div className="overflow-hidden">
                                    <div className="text-xs mb-2 font-semibold text-gray-600">对比表格样式</div>
                                    <table className="w-full text-xs">
                                      <thead>
                                        <tr>
                                          {table.headers.map((header, i) => (
                                            <th key={i} 
                                                className="p-2 text-center font-semibold rounded-t border"
                                                style={{
                                                  backgroundColor: theme.accentLight, 
                                                  color: theme.primary,
                                                  borderColor: theme.accentBorder
                                                }}>
                                              {header}
                                            </th>
                                          ))}
                                        </tr>
                                      </thead>
                                      <tbody>
                                        {table.rows.map((row, i) => (
                                          <tr key={i}>
                                            {row.map((cell, j) => (
                                              <td key={j} 
                                                  className="p-2 border border-l-2" 
                                                  style={{
                                                    borderLeftColor: theme.accentBorder,
                                                    backgroundColor: '#FAFAFA',
                                                    borderColor: '#E0E0E0'
                                                  }}>
                                                {cell}
                                              </td>
                                            ))}
                                          </tr>
                                        ))}
                                      </tbody>
                                    </table>
                                  </div>
                                ) : <div className="text-xs text-red-500">表格数据未找到</div>;
                              })()}
                            </div>
                          ) : slide.template === 'table' ? (
                            <div>
                              <h2 className="text-lg font-bold mb-3" style={{color: theme.primary}}>
                                {slide.title}
                              </h2>
                              {(() => {
                                const table = slide.content.find(item => item.type === 'table');
                                return table ? (
                                  <div className="overflow-hidden">
                                    <table className="w-full text-xs border-collapse border border-gray-200">
                                      <thead>
                                        <tr>
                                          {table.headers.map((header, i) => (
                                            <th key={i} 
                                                className="p-2 text-left font-semibold text-white"
                                                style={{backgroundColor: theme.primary}}>
                                              {header}
                                            </th>
                                          ))}
                                        </tr>
                                      </thead>
                                      <tbody>
                                        {table.rows.map((row, i) => (
                                          <tr key={i} className={i % 2 === 0 ? 'bg-gray-50' : 'bg-white'}>
                                            {row.map((cell, j) => (
                                              <td key={j} className="p-2 border-b border-gray-200">
                                                {cell}
                                              </td>
                                            ))}
                                          </tr>
                                        ))}
                                      </tbody>
                                    </table>
                                  </div>
                                ) : null;
                              })()}
                            </div>
                          ) : slide.template === 'columns' ? (
                            <div>
                              <h2 className="text-lg font-bold mb-3" style={{color: theme.primary}}>
                                {slide.title}
                              </h2>
                              <div className="grid grid-cols-2 gap-4">
                                {(() => {
                                  const nonTableContent = slide.content.filter(item => item.type !== 'table');
                                  const separated = slide.separatedContent || separateContentForColumns(nonTableContent);
                                  return (
                                    <>
                                      <div>
                                        <h3 className="font-semibold mb-2" style={{color: theme.primary}}>
                                          {separated.col1.title}
                                        </h3>
                                        <ul className="text-xs space-y-1">
                                          {separated.col1.content.map((item, i) => (
                                            <li key={i}>• {item.text}</li>
                                          ))}
                                        </ul>
                                      </div>
                                      <div>
                                        <h3 className="font-semibold mb-2" style={{color: theme.primary}}>
                                          {separated.col2.title}
                                        </h3>
                                        <ul className="text-xs space-y-1">
                                          {separated.col2.content.map((item, i) => (
                                            <li key={i}>• {item.text}</li>
                                          ))}
                                        </ul>
                                      </div>
                                    </>
                                  );
                                })()}
                              </div>
                            </div>
                          ) : slide.template === 'timeline' ? (
                            <div>
                              <h2 className="text-lg font-bold mb-3" style={{color: theme.primary}}>
                                {slide.title}
                              </h2>
                              <div className="flex justify-between items-start">
                                {(() => {
                                  const timelineContent = slide.content.filter(item => item.type !== 'table');
                                  return timelineContent.slice(0, 4).map((item, i) => (
                                    <div key={i} className="text-center flex-1">
                                      <div 
                                        className="w-6 h-6 rounded-full mx-auto mb-1 flex items-center justify-center text-xs font-bold"
                                        style={{backgroundColor: theme.accentLight, color: theme.accent}}
                                      >
                                        {i + 1}
                                      </div>
                                      <div className="text-xs">
                                        <div className="font-semibold">
                                          {item.text.split(':')[0]}
                                        </div>
                                        <div className="text-gray-600">
                                          {item.text.includes(':') ? item.text.split(':')[1] : ''}
                                        </div>
                                      </div>
                                    </div>
                                  ));
                                })()}
                              </div>
                            </div>
                          ) : (
                            <div>
                              <h2 className="text-lg font-bold mb-3" style={{color: theme.primary}}>
                                {slide.title}
                              </h2>
                              {(() => {
                                const nonTableContent = slide.content.filter(item => item.type !== 'table');
                                const tableContent = slide.content.filter(item => item.type === 'table');
                                return (
                                  <>
                                    <ul className="text-xs space-y-1 mb-3">
                                      {nonTableContent.map((item, i) => (
                                        <li key={i}>
                                          {item.type === 'bullet' ? '• ' : ''}{item.text}
                                        </li>
                                      ))}
                                    </ul>
                                    {tableContent.map((table, i) => (
                                      <div key={i} className="overflow-hidden mb-3">
                                        <table className="w-full text-xs border-collapse border border-gray-200">
                                          <thead>
                                            <tr>
                                              {table.headers.map((header, j) => (
                                                <th key={j} 
                                                    className="p-1 text-left font-semibold text-white text-xs"
                                                    style={{backgroundColor: theme.primary}}>
                                                  {header}
                                                </th>
                                              ))}
                                            </tr>
                                          </thead>
                                          <tbody>
                                            {table.rows.map((row, j) => (
                                              <tr key={j} className={j % 2 === 0 ? 'bg-gray-50' : 'bg-white'}>
                                                {row.map((cell, k) => (
                                                  <td key={k} className="p-1 border-b border-gray-200 text-xs">
                                                    {cell}
                                                  </td>
                                                ))}
                                              </tr>
                                            ))}
                                          </tbody>
                                        </table>
                                      </div>
                                    ))}
                                  </>
                                );
                              })()}
                            </div>
                          )}
                        </div>
                      </div>
                    ))}
                  )}
                </div>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Features Info */}
        <div className="bg-white rounded-lg shadow-lg p-6">
          <h2 className="text-xl font-semibold mb-4">智能功能说明</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
            <div className="p-4 bg-blue-50 rounded-lg">
              <Columns className="w-6 h-6 text-blue-600 mb-2" />
              <h3 className="font-semibold mb-1">智能分栏</h3>
              <p className="text-sm text-gray-600">自动识别对比内容，智能分配到左右栏</p>
            </div>
            <div className="p-4 bg-green-50 rounded-lg">
              <Clock className="w-6 h-6 text-green-600 mb-2" />
              <h3 className="font-semibold mb-1">时间线检测</h3>
              <p className="text-sm text-gray-600">识别流程和阶段，自动生成时间线布局</p>
            </div>
            <div className="p-4 bg-purple-50 rounded-lg">
              <Triangle className="w-6 h-6 text-purple-600 mb-2" />
              <h3 className="font-semibold mb-1">层次结构</h3>
              <p className="text-sm text-gray-600">特征列表自动优化为金字塔结构</p>
            </div>
            <div className="p-4 bg-orange-50 rounded-lg">
              <Target className="w-6 h-6 text-orange-600 mb-2" />
              <h3 className="font-semibold mb-1">模板推荐</h3>
              <p className="text-sm text-gray-600">基于内容特征智能推荐最佳模板</p>
            </div>
            <div className="p-4 bg-red-50 rounded-lg">
              <FileText className="w-6 h-6 text-red-600 mb-2" />
              <h3 className="font-semibold mb-1">表格识别</h3>
              <p className="text-sm text-gray-600">自动解析Markdown表格，生成专业对比布局</p>
            </div>
          </div>
          
          <div className="mt-6 p-4 bg-yellow-50 rounded-lg">
            <h3 className="font-semibold mb-2 text-yellow-800">📋 表格使用说明：</h3>
            <div className="text-sm text-gray-700 space-y-1">
              <p>• 使用标准Markdown表格语法：<code className="bg-gray-200 px-1 rounded">| 列1 | 列2 |</code></p>
              <p>• 两列表格会自动识别为对比表格，使用特殊的对比样式</p>
              <p>• 多列表格使用标准表格样式，适合数据展示</p>
              <p>• 系统会自动跳过分隔符行（包含 - 和 : 的行）</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PPTGenerator;