<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Digital Language Museums: Preserving Linguistic Heritage</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;700;900&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background-color: #f0f2f5;
            color: #1a202c;
            overflow: hidden;
        }
        .slide {
            display: none;
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            padding: 2rem 4rem; /* Adjusted padding */
            box-sizing: border-box;
            background-color: white;
            opacity: 0;
            transition: opacity 0.6s ease-in-out;
            align-items: center;
            justify-content: center;
            flex-direction: column;
        }
        .slide.active {
            display: flex;
            opacity: 1;
            z-index: 10;
        }
        .slide-content {
            width: 100%;
            max-width: 1200px;
            margin: auto;
        }
        h1 {
            font-weight: 900;
            font-size: 3.5rem;
            color: #003366; /* Deep Blue */
            line-height: 1.2;
        }
        h2 {
            font-weight: 700;
            font-size: 1.5rem;
            color: #FF6600; /* Warm Orange */
            margin-top: 0.5rem;
        }
        h3 {
            font-weight: 700;
            font-size: 2.5rem;
            color: #003366;
            margin-bottom: 2rem;
            text-align: center;
        }
        p, li {
            font-size: 1.25rem;
            line-height: 1.8;
            color: #4a5568;
        }
        .footer {
            position: absolute;
            bottom: 2rem;
            left: 50%;
            transform: translateX(-50%);
            font-size: 0.875rem;
            color: #718096;
        }
        .nav-arrow {
            position: fixed;
            top: 50%;
            transform: translateY(-50%);
            z-index: 20;
            background-color: rgba(0, 51, 102, 0.7);
            color: white;
            border: none;
            border-radius: 50%;
            width: 3rem;
            height: 3rem;
            font-size: 1.5rem;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        .nav-arrow:hover {
            background-color: #FF6600;
        }
        #prev-btn { left: 1rem; }
        #next-btn { right: 1rem; }
        .slide-counter {
            position: fixed;
            bottom: 1rem;
            right: 1rem;
            z-index: 20;
            background-color: rgba(0, 0, 0, 0.5);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 1rem;
            font-size: 0.875rem;
        }
        .icon-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 2rem;
            text-align: center;
        }
        .icon-item svg {
            width: 64px;
            height: 64px;
            margin: 0 auto 1rem;
            color: #003366;
        }
        .checklist li {
            list-style: none;
            padding-left: 2.5rem;
            position: relative;
        }
        .checklist li::before {
            content: '✓';
            position: absolute;
            left: 0;
            color: #FF6600;
            font-weight: bold;
            font-size: 1.5rem;
        }
        /* Gemini Feature Styles */
        .gemini-btn {
            background-color: #FF6600;
            color: white;
            font-weight: bold;
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            transition: all 0.3s;
            cursor: pointer;
            border: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }
        .gemini-btn:hover {
            background-color: #003366;
            transform: translateY(-2px);
            box-shadow: 0 4px 10px rgba(0,0,0,0.1);
        }
        .gemini-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.6);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 100;
            opacity: 0;
            visibility: hidden;
            transition: opacity 0.3s, visibility 0.3s;
        }
        .gemini-modal.visible {
            opacity: 1;
            visibility: visible;
        }
        .gemini-modal-content {
            background-color: white;
            padding: 2rem;
            border-radius: 1rem;
            width: 90%;
            max-width: 800px;
            max-height: 80vh;
            overflow-y: auto;
            transform: scale(0.9);
            transition: transform 0.3s;
        }
        .gemini-modal.visible .gemini-modal-content {
            transform: scale(1);
        }
        .loader {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #FF6600;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 1rem auto;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>

    <!-- Slides 1-7 (unchanged) -->
    <section id="slide-1" class="slide active" style="background-image: url('https://placehold.co/1920x1080/003366/FFFFFF?text=Digital+Connections'); background-size: cover; background-position: center;">
        <div class="text-center bg-white/80 p-12 rounded-lg shadow-2xl">
            <h1 class="text-6xl">Digital Language Museums</h1>
            <h2 class="text-3xl mt-4">Preserving Linguistic Heritage in the Digital Age</h2>
            <p class="mt-8 text-xl text-gray-700">An Introduction to Digital Humanities</p>
        </div>
        <div class="footer text-white">One-on-One Tutorial Session with Patrick</div>
    </section>
    <section id="slide-2" class="slide">
        <div class="slide-content text-center">
            <h3 class="text-5xl mb-8">What Happens When a Language Dies?</h3>
            <div class="grid md:grid-cols-2 gap-8 items-center">
                <div class="text-left space-y-6 text-2xl">
                    <p>Every <strong class="text-orange-600">2 weeks</strong>, a language disappears forever.</p>
                    <p>We lose more than words – we lose entire <strong class="text-orange-600">worldviews</strong>.</p>
                    <p>How can <strong class="text-orange-600">technology</strong> help preserve this heritage?</p>
                </div>
                <img src="https://placehold.co/600x400/FF6600/FFFFFF?text=Endangered+Languages+Map" alt="World map showing endangered languages" class="rounded-lg shadow-lg mx-auto">
            </div>
        </div>
    </section>
    <section id="slide-3" class="slide">
        <div class="slide-content text-center">
            <h3>Digital Humanities: A New Lens for Ancient Questions</h3>
            <div class="grid md:grid-cols-2 gap-8 items-center mt-8">
                <div class="border-r-2 border-gray-200 pr-8">
                    <h4 class="text-2xl font-bold text-blue-900 mb-4">Traditional Humanities = Magnifying glass 🔍</h4>
                    <img src="https://placehold.co/500x300/EEEEEE/333333?text=Old+Manuscript" alt="Traditional research" class="rounded-lg shadow-md mx-auto">
                    <p class="mt-4">Deep, focused analysis of individual texts and artifacts.</p>
                </div>
                <div>
                    <h4 class="text-2xl font-bold text-orange-600 mb-4">Digital Humanities = Forensics lab 🔬</h4>
                    <img src="https://placehold.co/500x300/003366/FFFFFF?text=Data+Visualization" alt="Digital research" class="rounded-lg shadow-md mx-auto">
                    <p class="mt-4">Analyzing vast patterns across millions of data points.</p>
                </div>
            </div>
            <p class="mt-12 text-2xl font-bold">Same mysteries, powerful new tools.</p>
        </div>
    </section>
    <section id="slide-4" class="slide">
        <div class="slide-content text-center">
            <h3>Uncovering Hidden Patterns in Human Culture</h3>
            <div class="bg-white p-6 rounded-lg shadow-xl">
                <img src="https://placehold.co/900x500/E0E0E0/333333?text=Google+Ngram+for+'Privacy'" alt="Google Ngram graph showing 'privacy' usage from 1800-2000" class="w-full rounded-md">
            </div>
            <div class="mt-8 text-left max-w-4xl mx-auto grid md:grid-cols-2 gap-6">
                <p class="text-xl">We can track word usage across millions of books.</p>
                <p class="text-xl">The concept of <strong class="text-orange-600">"privacy"</strong> as we know it barely existed before the 1890s.</p>
                <p class="text-xl col-span-2 text-center mt-4">What does this tell us about societal change?</p>
            </div>
        </div>
    </section>
    <section id="slide-5" class="slide">
        <div class="slide-content text-center">
            <h3>Reimagining the Museum Experience</h3>
            <div class="grid md:grid-cols-2 gap-12 mt-8 text-left">
                <div class="p-6 bg-gray-100 rounded-lg">
                    <h4 class="text-2xl font-bold text-blue-900 mb-4">Traditional Museum</h4>
                    <ul class="space-y-3 text-lg">
                        <li>▶️ Static displays</li>
                        <li>▶️ Limited physical access</li>
                        <li>▶️ Passive observation</li>
                        <li>▶️ Fixed, linear narratives</li>
                    </ul>
                </div>
                <div class="p-6 bg-blue-50 rounded-lg">
                    <h4 class="text-2xl font-bold text-orange-600 mb-4">Digital Language Museum</h4>
                    <ul class="space-y-3 text-lg">
                        <li>✅ Interactive experiences</li>
                        <li>✅ Global, 24/7 reach</li>
                        <li>✅ Active community participation</li>
                        <li>✅ Evolving, interconnected stories</li>
                    </ul>
                </div>
            </div>
        </div>
    </section>
    <section id="slide-6" class="slide">
        <div class="slide-content text-center">
            <h3>Case Study: Endangered Languages Digital Archive (ELAR)</h3>
            <div class="grid md:grid-cols-2 gap-8 items-center mt-8">
                <img src="https://placehold.co/600x450/003366/FFFFFF?text=ELAR+Interface" alt="Screenshot of ELAR interface" class="rounded-lg shadow-lg mx-auto">
                <div class="text-left space-y-4">
                    <p>Home to over <strong class="text-orange-600">500 language</strong> deposits from around the world.</p>
                    <p>Rich multimedia documentation: video, audio, and text.</p>
                    <p>Focuses on cultural context, stories, and community contributions.</p>
                    <a href="https://elar.soas.ac.uk/" target="_blank" class="inline-block mt-4 bg-orange-600 text-white font-bold py-2 px-4 rounded hover:bg-orange-700 transition">Explore ELAR</a>
                </div>
            </div>
        </div>
    </section>
    <section id="slide-7" class="slide">
        <div class="slide-content text-center">
            <h3>Beyond Archive: Creating Living Language Spaces</h3>
            <p class="mb-8 text-xl">Our Vision: A Cantonese Emotion Digital Museum</p>
            <div class="grid md:grid-cols-2 gap-8 items-center">
                 <div class="text-left space-y-4">
                    <h4 class="text-2xl font-bold text-blue-900">"Heart's Journey Gallery" (心的旅程展厅)</h4>
                    <p>Experience emotions across cultures.</p>
                    <p>Hear the sounds, visualize the connections, understand the nuances.</p>
                    <p>Bridge linguistic divides through shared human experience.</p>
                </div>
                <img src="https://placehold.co/600x450/FFFFFF/003366?text=Interactive+Emotion+Network" alt="Conceptual mockup of museum interface" class="rounded-lg shadow-2xl border-4 border-blue-900 mx-auto">
            </div>
        </div>
    </section>

    <!-- Slide 8: The Power of Context (with Gemini) -->
    <section id="slide-8" class="slide">
        <div class="slide-content text-center">
            <h3>More Than Words: Preserving Cultural Meaning</h3>
            <div class="mt-4 max-w-3xl mx-auto bg-gray-50 p-8 rounded-xl shadow-lg">
                <p class="text-4xl font-bold mb-6">"食咗飯未?"</p>
                <p class="text-xl mb-8">(sihk jó faahn meih?)</p>
                <div class="text-left space-y-6">
                    <div class="p-4 border-l-4 border-blue-300 bg-blue-50 rounded-r-lg">
                        <p><strong class="text-blue-900">Surface Meaning 🍚:</strong> "Have you eaten rice yet?" - A simple question about food.</p>
                    </div>
                    <div class="p-4 border-l-4 border-orange-300 bg-orange-50 rounded-r-lg">
                        <p><strong class="text-orange-600">Cultural Meaning ❤️:</strong> "I care about you. Are you doing okay?" - An expression of care and connection.</p>
                    </div>
                </div>
                 <p class="mt-6 text-lg">True preservation requires understanding the <strong class="text-blue-900">context</strong>: when, how, and why something is said.</p>
            </div>
            <button id="explore-connections-btn" class="gemini-btn mt-6">✨ Explore Deeper Connections</button>
        </div>
    </section>

    <!-- Slides 9-11 (unchanged) -->
    <section id="slide-9" class="slide">
        <div class="slide-content text-center">
            <h3>Three Pillars of Impact</h3>
            <div class="flex flex-col md:flex-row justify-center items-center gap-8 md:-space-x-8 mt-12">
                <div class="w-64 h-64 rounded-full bg-blue-100 flex flex-col justify-center items-center p-4 shadow-lg text-center">
                    <h4 class="text-2xl font-bold text-blue-900">Preservation 💾</h4>
                    <p class="text-sm mt-2">Capture languages and cultural knowledge before they vanish.</p>
                </div>
                <div class="w-72 h-72 rounded-full bg-orange-100 flex flex-col justify-center items-center p-4 shadow-xl text-center z-10">
                    <h4 class="text-3xl font-bold text-orange-600">Education 🎓</h4>
                    <p class="text-base mt-2">Provide global, interactive learning experiences for everyone.</p>
                </div>
                <div class="w-64 h-64 rounded-full bg-blue-100 flex flex-col justify-center items-center p-4 shadow-lg text-center">
                    <h4 class="text-2xl font-bold text-blue-900">Research 🔬</h4>
                    <p class="text-sm mt-2">Create rich datasets for new computational and linguistic discoveries.</p>
                </div>
            </div>
        </div>
    </section>
    <section id="slide-10" class="slide">
        <div class="slide-content text-center">
            <h3>Building Bridges Between Technology and Humanity</h3>
            <p class="text-xl mb-8">Your Learning Path:</p>
            <div class="relative w-full max-w-4xl mx-auto">
                <div class="absolute left-1/2 top-4 bottom-4 w-1 bg-blue-200 -translate-x-1/2"></div>
                <div class="space-y-12">
                    <div class="relative"><div class="absolute left-1/2 top-1/2 w-4 h-4 bg-orange-600 rounded-full -translate-x-1/2 -translate-y-1/2 z-10"></div><div class="md:w-1/2 md:pr-8 md:text-right"><strong>Weeks 1-6:</strong> Python & Linguistic Data Processing</div></div>
                    <div class="relative"><div class="absolute left-1/2 top-1/2 w-4 h-4 bg-orange-600 rounded-full -translate-x-1/2 -translate-y-1/2 z-10"></div><div class="md:w-1/2 md:pl-8 md:ml-auto md:text-left"><strong>Weeks 7-17:</strong> Bilingual Corpus Construction</div></div>
                    <div class="relative"><div class="absolute left-1/2 top-1/2 w-4 h-4 bg-orange-600 rounded-full -translate-x-1/2 -translate-y-1/2 z-10"></div><div class="md:w-1/2 md:pr-8 md:text-right"><strong>Weeks 18-28:</strong> Museum Design & Implementation</div></div>
                    <div class="relative"><div class="absolute left-1/2 top-1/2 w-4 h-4 bg-orange-600 rounded-full -translate-x-1/2 -translate-y-1/2 z-10"></div><div class="md:w-1/2 md:pl-8 md:ml-auto md:text-left"><strong>Weeks 29-32:</strong> Impact & Dissemination</div></div>
                </div>
            </div>
        </div>
    </section>
    <section id="slide-11" class="slide">
        <div class="slide-content text-center">
            <h3>Your Digital Humanities Toolkit</h3>
            <p class="mb-12 text-lg">You'll gain practical, marketable skills.</p>
            <div class="icon-grid max-w-4xl mx-auto">
                <div class="icon-item"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M14.25 9.75L16.5 12l-2.25 2.25m-4.5 0L7.5 12l2.25-2.25M6 20.25h12A2.25 2.25 0 0020.25 18V6A2.25 2.25 0 0018 3.75H6A2.25 2.25 0 003.75 6v12A2.25 2.25 0 006 20.25z" /></svg><p>Python Programming</p></div>
                <div class="icon-item"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M3.75 3v11.25A2.25 2.25 0 006 16.5h12M3.75 3h-1.5m1.5 0h16.5m0 0h1.5m-1.5 0v11.25A2.25 2.25 0 0118 16.5h-12a2.25 2.25 0 01-2.25-2.25V3.75m16.5 0v16.5h-16.5" /></svg><p>Data Visualization</p></div>
                <div class="icon-item"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M20.25 6.375c0 2.278-3.694 4.125-8.25 4.125S3.75 8.653 3.75 6.375m16.5 0c0-2.278-3.694-4.125-8.25-4.125S3.75 4.097 3.75 6.375m16.5 0v11.25c0 2.278-3.694 4.125-8.25 4.125s-8.25-1.847-8.25-4.125V6.375m16.5 0v3.75m-16.5-3.75v3.75m16.5 0v3.75C20.25 16.153 16.556 18 12 18s-8.25-1.847-8.25-4.125v-3.75" /></svg><p>Database Design</p></div>
                <div class="icon-item"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M12 21a9.004 9.004 0 008.716-6.747M12 21a9.004 9.004 0 01-8.716-6.747M12 21c2.485 0 4.5-4.03 4.5-9S14.485 3 12 3m0 18c-2.485 0-4.5-4.03-4.5-9S9.515 3 12 3m0 0a8.997 8.997 0 017.843 4.582M12 3a8.997 8.997 0 00-7.843 4.582m15.686 0A11.953 11.953 0 0112 10.5c-2.998 0-5.74-1.1-7.843-2.918m15.686 0A8.959 8.959 0 0121 12c0 .778-.099 1.533-.284 2.253m0 0A17.919 17.919 0 0112 16.5c-3.162 0-6.133-.815-8.716-2.247m0 0A9.015 9.015 0 013 12c0-1.605.42-3.113 1.157-4.418" /></svg><p>Web Development</p></div>
                <div class="icon-item"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M12 18.75a6 6 0 006-6v-1.5m-6 7.5a6 6 0 01-6-6v-1.5m6 7.5v3.75m-3.75 0h7.5M12 15.75a3 3 0 01-3-3V4.5a3 3 0 116 0v8.25a3 3 0 01-3 3z" /></svg><p>Acoustic Analysis</p></div>
                <div class="icon-item"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M8.25 3v1.5M4.5 8.25H3m18 0h-1.5M4.5 12H3m18 0h-1.5m-15 3.75H3m18 0h-1.5M8.25 19.5V21M12 3v1.5m0 15V21m3.75-18v1.5m0 15V21m-9-1.5h10.5a2.25 2.25 0 002.25-2.25V8.25a2.25 2.25 0 00-2.25-2.25H6.75A2.25 2.25 0 004.5 8.25v10.5A2.25 2.25 0 006.75 21z" /></svg><p>Natural Language Processing</p></div>
            </div>
        </div>
    </section>

    <!-- Slide 12: First Assignment (with Gemini) -->
    <section id="slide-12" class="slide">
        <div class="slide-content text-center">
            <h3>First Assignment: Your Museum Concept Book</h3>
            <div class="grid md:grid-cols-2 gap-8 items-start mt-4">
                <div class="text-left space-y-4">
                    <h4 class="text-2xl font-bold text-blue-900 mb-4">Key Questions:</h4>
                    <p><strong>Audience:</strong> Who are we designing this for?</p>
                    <p><strong>Narrative:</strong> What is the core story we want to tell?</p>
                    <p><strong>Experience:</strong> How will visitors interact?</p>
                    <p class="mt-4 font-bold text-orange-600">Deliverable: A draft outline is due for our next session.</p>
                </div>
                <div class="text-left bg-gray-50 p-6 rounded-lg shadow-inner">
                    <h4 class="text-2xl font-bold text-blue-900 mb-4">Need inspiration?</h4>
                    <p class="mb-2">Enter a theme to brainstorm ideas:</p>
                    <input type="text" id="concept-theme-input" placeholder="e.g., Cantonese food idioms" class="w-full p-2 border border-gray-300 rounded-md mb-2">
                    <button id="generate-concept-btn" class="gemini-btn">✨ Generate Museum Ideas</button>
                    <div id="concept-results" class="mt-4 text-sm space-y-4">
                        <!-- Gemini results will be injected here -->
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Remaining slides (13-16) and Navigation -->
    <section id="slide-13" class="slide">
        <div class="slide-content text-center">
            <h3>From Raw Data to Museum Exhibit</h3>
            <p class="mb-12 text-lg">This is the concrete process we'll follow.</p>
            <div class="space-y-8">
                <div class="p-6 bg-gray-100 rounded-lg shadow-md"><p class="text-2xl">Cantonese Raw Data: <strong class="text-blue-900">我好開心見到你！</strong> (I'm so happy to see you!)</p></div>
                <div class="text-4xl text-orange-600 font-bold">↓</div>
                <div class="grid grid-cols-3 gap-4 text-center">
                    <div class="p-4 bg-blue-50 rounded-lg">[Acoustic Analysis]</div>
                    <div class="p-4 bg-blue-50 rounded-lg">[Semantic Networks]</div>
                    <div class="p-4 bg-blue-50 rounded-lg">[Interactive Display]</div>
                </div>
                <div class="text-4xl text-orange-600 font-bold">↓</div>
                <div class="p-8 bg-orange-100 rounded-lg shadow-xl"><p class="text-3xl font-bold text-orange-700">A Meaningful Museum Visitor Experience</p></div>
            </div>
        </div>
    </section>
    <section id="slide-14" class="slide">
        <div class="slide-content text-center">
            <h3>Before Our Next Session</h3>
            <div class="mt-8 max-w-2xl mx-auto bg-white p-8 rounded-xl shadow-lg">
                <ul class="checklist text-left space-y-4 text-xl">
                    <li>Explore the ELAR website and find one entry you find interesting.</li>
                    <li>Draft a simple one-page outline for your museum concept book.</li>
                    <li>Do a quick search on how "happiness" (開心) is expressed in Cantonese culture.</li>
                    <li>Think: How would YOU personally want to experience language in a digital space?</li>
                </ul>
            </div>
        </div>
    </section>
    <section id="slide-15" class="slide" style="background-image: url('https://placehold.co/1920x1080/002040/FFFFFF?text=Global+Community'); background-size: cover; background-position: center;">
         <div class="text-center bg-white/80 p-12 rounded-lg shadow-2xl max-w-4xl">
            <h3 class="text-5xl">You're Not Just Building a Website...</h3>
            <div class="mt-8 space-y-6 text-2xl text-gray-800">
                <p>You're creating a <strong class="text-blue-900">bridge between cultures</strong> 🌉</p>
                <p>You're building a <strong class="text-blue-900">time capsule for future generations</strong> ⏰</p>
                <p>You're designing a <strong class="text-blue-900">new model for digital preservation</strong> 💡</p>
                <p>You're making a space where languages <strong class="text-orange-600">live</strong>, not just survive 🌱</p>
            </div>
        </div>
    </section>
    <section id="slide-16" class="slide">
        <div class="slide-content text-center">
            <h3>Let's Explore Your Ideas</h3>
            <img src="https://placehold.co/600x300/FFFFFF/333333?text=❓➡️💡" alt="Question marks transforming into lightbulbs" class="my-8 mx-auto">
            <div class="text-left max-w-3xl mx-auto space-y-4 text-xl">
                <p>What excites you most about this project?</p>
                <p>Which technical skills are you most eager to learn?</p>
                <p>How do you envision the final museum experience?</p>
            </div>
        </div>
    </section>

    <!-- Navigation -->
    <button id="prev-btn" class="nav-arrow">‹</button>
    <button id="next-btn" class="nav-arrow">›</button>
    <div id="slide-counter" class="slide-counter">1 / 16</div>

    <!-- Gemini Modal -->
    <div id="gemini-modal" class="gemini-modal">
        <div id="gemini-modal-content" class="gemini-modal-content">
            <!-- Content will be injected by JS -->
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            // --- Basic Slide Navigation ---
            const slides = document.querySelectorAll('.slide');
            const prevBtn = document.getElementById('prev-btn');
            const nextBtn = document.getElementById('next-btn');
            const slideCounter = document.getElementById('slide-counter');
            let currentSlide = 0;
            const totalSlides = slides.length;

            function showSlide(index) {
                if (slides[currentSlide]) slides[currentSlide].classList.remove('active');
                currentSlide = (index + totalSlides) % totalSlides;
                if (slides[currentSlide]) slides[currentSlide].classList.add('active');
                if (slideCounter) slideCounter.textContent = `${currentSlide + 1} / ${totalSlides}`;
                if (prevBtn) prevBtn.style.display = currentSlide === 0 ? 'none' : 'block';
                if (nextBtn) nextBtn.style.display = currentSlide === totalSlides - 1 ? 'none' : 'block';
            }

            if (prevBtn) prevBtn.addEventListener('click', () => showSlide(currentSlide - 1));
            if (nextBtn) nextBtn.addEventListener('click', () => showSlide(currentSlide + 1));
            
            document.addEventListener('keydown', (e) => {
                if (e.key === 'ArrowRight' && currentSlide < totalSlides - 1) {
                    showSlide(currentSlide + 1);
                } else if (e.key === 'ArrowLeft' && currentSlide > 0) {
                    showSlide(currentSlide - 1);
                }
            });
            
            showSlide(0);

            // --- Gemini API Integration ---
            const modal = document.getElementById('gemini-modal');
            const modalContent = document.getElementById('gemini-modal-content');
            const exploreBtn = document.getElementById('explore-connections-btn');
            const generateConceptBtn = document.getElementById('generate-concept-btn');
            const conceptInput = document.getElementById('concept-theme-input');
            const conceptResultsContainer = document.getElementById('concept-results');

            // Function to call Gemini API
            async function callGemini(prompt) {
                const apiKey = ""; // Leave empty, will be handled by the environment
                const apiUrl = `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${apiKey}`;
                
                const payload = {
                    contents: [{ role: "user", parts: [{ text: prompt }] }]
                };

                try {
                    const response = await fetch(apiUrl, {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify(payload)
                    });

                    if (!response.ok) {
                        throw new Error(`API Error: ${response.statusText}`);
                    }

                    const result = await response.json();
                    
                    if (result.candidates && result.candidates.length > 0 &&
                        result.candidates[0].content && result.candidates[0].content.parts &&
                        result.candidates[0].content.parts.length > 0) {
                        return result.candidates[0].content.parts[0].text;
                    } else {
                        console.error("Unexpected API response structure:", result);
                        return "Sorry, I couldn't get a valid response. The response from the model was empty or malformed.";
                    }
                } catch (error) {
                    console.error("Error calling Gemini API:", error);
                    return `Sorry, an error occurred: ${error.message}. Please check the console for details.`;
                }
            }
            
            // --- Feature 1: Explore Connections ---
            if (exploreBtn) {
                exploreBtn.addEventListener('click', async () => {
                    modalContent.innerHTML = '<h2 class="text-2xl font-bold text-blue-900 mb-4">Discovering More...</h2><div class="loader"></div><p>Asking the AI to find other Cantonese phrases with deep cultural meanings...</p>';
                    modal.classList.add('visible');

                    const prompt = `The Cantonese phrase "食咗飯未?" (Have you eaten yet?) is not just about food, it's a way of showing care. Please provide 3 more examples of Cantonese phrases like this, which have a simple literal meaning but a deeper, more important cultural meaning. For each example, provide:
1. The phrase in Cantonese characters.
2. The Yale romanization.
3. The literal English translation.
4. A clear explanation of the deeper cultural meaning or context.
Format the response clearly for a presentation.`;
                    
                    const resultText = await callGemini(prompt);
                    modalContent.innerHTML = `
                        <div class="flex justify-between items-center mb-4">
                            <h2 class="text-2xl font-bold text-blue-900">Deeper Connections</h2>
                            <button id="close-modal-btn" class="text-2xl font-bold">&times;</button>
                        </div>
                        <div class="prose max-w-none">${resultText.replace(/\n/g, '<br>')}</div>
                    `;
                    document.getElementById('close-modal-btn').addEventListener('click', () => modal.classList.remove('visible'));
                });
            }
            
            // --- Feature 2: Generate Concept Ideas ---
            if (generateConceptBtn) {
                generateConceptBtn.addEventListener('click', async () => {
                    const theme = conceptInput.value.trim();
                    if (!theme) {
                        conceptResultsContainer.innerHTML = '<p class="text-red-500">Please enter a theme first.</p>';
                        return;
                    }
                    conceptResultsContainer.innerHTML = '<div class="loader"></div><p>Brainstorming with AI...</p>';
                    
                    const prompt = `I am creating a concept for a digital museum about Cantonese language. My chosen theme is "${theme}". Based on this theme, please generate creative ideas for the following three sections:
1.  **Target Audience:** Who would be most interested in this? Be specific (e.g., "second-generation diaspora youth," "linguistics students").
2.  **Narrative:** What is a compelling story or main message we can tell about this theme?
3.  **Interactive Experience:** Suggest one specific, creative interactive feature for the digital museum that would engage visitors with this theme.
Keep the descriptions concise and inspiring.`;

                    const resultText = await callGemini(prompt);
                    conceptResultsContainer.innerHTML = `<div class="prose-sm max-w-none">${resultText.replace(/\n/g, '<br>')}</div>`;
                });
            }

            // Close modal on outside click
            if (modal) {
                modal.addEventListener('click', (e) => {
                    if (e.target === modal) {
                        modal.classList.remove('visible');
                    }
                });
            }
        });
    </script>
</body>
</html>
