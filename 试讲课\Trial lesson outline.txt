# Introduction to Digital Humanities and Language Museums

## Digital Language Museums: Preserving Linguistic Heritage in the Digital Age
Subtitle: An Introduction to Digital Humanities

## Opening Question

What Happens When a Language Dies?

* Every 2 weeks, a language disappears forever

* We lose more than words – we lose entire worldviews

* How can technology help preserve linguistic heritage? Visual: World map showing endangered languages or countdown timer Speaker Note: Engage <PERSON> with thought-provoking question

## Slide 3: What is Digital Humanities?

Title: Digital Humanities: A New Lens for Ancient Questions
Content:

* Traditional Humanities = Magnifying glass 🔍

* Digital Humanities = Forensics lab 🔬

* Same mysteries, powerful new tools Visual: Split screen showing traditional vs. digital research methods Speaker Note: Use detective analogy to make concept relatable

## Slide 4: DH in Action - Google Ngram Example

Title: Uncovering Hidden Patterns in Human Culture
Visual: Google Ngram graph showing "privacy" usage from 1800-2000 Content:

* Track word usage across millions of books

* "Privacy" barely existed before 1890s

* What does this tell us about society? Speaker Note: Concrete example to demonstrate DH power

## Slide 5: From Physical to Digital Museums

Title: Reimagining the Museum Experience
Content (Two columns):

| Traditional Museum                      | Digital Language Museum |
| :-------------------------------------- | :---------------------- |
| Static displays                         | Interactive experiences |
| Limited access                          | Global reach            |
| Observation only                        | Community participation |
| Fixed narratives                        | Evolving stories        |
| Visual: Side-by-side museum comparisons |                         |

## Slide 6: Case Study - ELAR

Title: Endangered Languages Digital Archive (ELAR)
Content:

* 500+ language deposits worldwide

* Rich multimedia documentation:

  * Video recordings in natural settings

  * Cultural context and stories

  * Community contributions Visual: Screenshot of ELAR interface Link: https://elar.soas.ac.uk/

## Slide 7: Our Vision - Cantonese Emotion Digital Museum

Title: Beyond Archive: Creating Living Language Spaces
Visual: Conceptual mockup of museum interface showing:

* "Heart's Journey Gallery" (心的旅程展厅)

* Interactive emotion networks

* Parallel text displays Content:

* Experience emotions across cultures

* Hear, visualize, understand

* Bridge linguistic divides

## Slide 8: The Power of Context

Title: More Than Words: Preserving Cultural Meaning
Example: "食咗飯未?" (Have you eaten rice yet?) Content:

* Surface: Question about food 🍚

* Culture: Expression of care ❤️

* Context: When, how, and why it's used Visual: Layered visualization showing literal vs. cultural meaning Speaker Note: Use this example to show depth of preservation needed

## Slide 9: Why Digital Language Museums Matter

Title: Three Pillars of Impact
Visual: Three interconnected circles

1. Preservation 💾

   * Capture languages before extinction

   * Document cultural knowledge

2. Education 🎓

   * Global accessibility

   * Interactive learning

3. Research 🔬

   * Rich datasets

   * Computational linguistics

## Slide 10: The Journey Ahead - Your Role

Title: Building Bridges Between Technology and Humanity
Content: Your learning path:

* Week 1-6: Python & linguistic data processing

* Week 7-17: Bilingual corpus construction

* Week 18-28: Museum design & implementation

* Week 29-32: Impact & dissemination Visual: Timeline or journey map

## Slide 11: Technical Skills You'll Master

Title: Your Digital Humanities Toolkit
Content (Icons with labels):

* 🐍 Python programming

* 📊 Data visualization (D3.js, Plotly)

* 🗃️ Database design (SQLite)

* 🌐 Web development (Flask/React)

* 🔊 Acoustic analysis (Praat)

* 🤖 Natural Language Processing Speaker Note: Emphasize practical, marketable skills

## Slide 12: First Assignment

Title: Your Museum Concept Book
Key Questions:

1. Audience: Who will visit our museum?

2. Narrative: What story do we tell?

3. Experience: How do we engage visitors? Visual: Template or example pages from a concept book Deliverable: Draft due next session

## Slide 13: Transforming Data into Experience

Title: From Raw Data to Museum Exhibit
Visual: Flow diagram showing transformation:

```
Cantonese: 我好開心見到你！
    ↓
[Acoustic Analysis] → [Semantic Networks] → [Interactive Display]
    ↓
Museum Visitor Experience

```

Speaker Note: Show concrete transformation process

## Slide 14: Next Steps

Title: Before Our Next Session
To Do List:

1. ✓ Explore ELAR website

2. ✓ Draft museum concept book outline

3. ✓ Research how "happiness" is expressed in Cantonese

4. ✓ Consider: How would YOU want to experience language? Visual: Checklist design

## Slide 15: Closing Thought

Title: You're Not Just Building a Website...
Content: You're creating:

* A bridge between cultures 🌉

* A time capsule for future generations ⏰

* A new model for digital preservation 💡

* A space where languages live, not just survive 🌱 Visual: Inspiring image of interconnected global communities

## Slide 16: Questions & Discussion

Title: Let's Explore Your Ideas&#x20;

Content:

* What excites you most about this project?

* Which technical skills are you eager to learn?

* How do you envision the museum experience? Visual: Question marks transforming into lightbulbs Speaker Note: End with open dialogue

