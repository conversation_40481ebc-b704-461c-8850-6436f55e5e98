<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简单功能测试</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .test-btn { background: #007bff; color: white; border: none; padding: 10px 20px; margin: 5px; border-radius: 5px; cursor: pointer; }
        .test-btn:hover { background: #0056b3; }
        #result { margin-top: 20px; padding: 10px; border: 1px solid #ddd; border-radius: 5px; background: #f9f9f9; }
    </style>
</head>
<body>
    <h1>PPT生成器功能测试</h1>
    
    <button class="test-btn" onclick="testGenerateFunction()">测试生成函数</button>
    <button class="test-btn" onclick="testParseFunction()">测试解析函数</button>
    <button class="test-btn" onclick="testButtonClick()">模拟按钮点击</button>
    
    <div id="result">等待测试...</div>

    <script>
        function log(message) {
            document.getElementById('result').innerHTML += message + '<br>';
        }

        function testGenerateFunction() {
            log('=== 测试生成函数 ===');
            
            // 测试基本的JavaScript功能
            try {
                const testSlides = [];
                const testTheme = {
                    primary: '#0D47A1',
                    accent: '#1976D2'
                };
                
                log('✅ 基本变量创建成功');
                
                // 测试简单的解析逻辑
                const testText = `# 测试标题
## 测试副标题
- 测试项目1
- 测试项目2`;
                
                const lines = testText.split('\n');
                let slideCount = 0;
                
                lines.forEach(line => {
                    const trimmed = line.trim();
                    if (trimmed.startsWith('# ')) {
                        slideCount++;
                        log(`发现标题: ${trimmed}`);
                    } else if (trimmed.startsWith('## ')) {
                        log(`发现副标题: ${trimmed}`);
                    } else if (trimmed.startsWith('- ')) {
                        log(`发现列表项: ${trimmed}`);
                    }
                });
                
                log(`✅ 解析完成，发现 ${slideCount} 个标题`);
                
            } catch (error) {
                log(`❌ 测试失败: ${error.message}`);
            }
        }

        function testParseFunction() {
            log('=== 测试解析函数 ===');
            
            try {
                // 模拟parseOutline函数的核心逻辑
                function simpleParseOutline(text) {
                    const slides = [];
                    const lines = text.split('\n');
                    let currentSlide = null;
                    
                    lines.forEach(line => {
                        const trimmed = line.trim();
                        
                        if (trimmed.startsWith('# ')) {
                            if (currentSlide) slides.push(currentSlide);
                            currentSlide = {
                                type: 'title',
                                title: trimmed.substring(2).trim(),
                                content: []
                            };
                        } else if (trimmed.startsWith('## ')) {
                            if (currentSlide && currentSlide.type === 'title') {
                                currentSlide.subtitle = trimmed.substring(3).trim();
                            } else {
                                if (currentSlide) slides.push(currentSlide);
                                currentSlide = {
                                    type: 'content',
                                    title: trimmed.substring(3).trim(),
                                    content: []
                                };
                            }
                        } else if (trimmed.startsWith('- ')) {
                            if (currentSlide) {
                                currentSlide.content.push({
                                    type: 'bullet',
                                    text: trimmed.substring(2).trim()
                                });
                            }
                        }
                    });
                    
                    if (currentSlide) slides.push(currentSlide);
                    return slides;
                }
                
                const testInput = `# 主标题
## 副标题
- 要点1
- 要点2
## 第二页
- 内容1
- 内容2`;
                
                const result = simpleParseOutline(testInput);
                log(`✅ 解析成功，生成 ${result.length} 张幻灯片`);
                
                result.forEach((slide, index) => {
                    log(`幻灯片 ${index + 1}: ${slide.title} (${slide.type})`);
                    if (slide.subtitle) log(`  副标题: ${slide.subtitle}`);
                    slide.content.forEach(item => {
                        log(`  - ${item.text}`);
                    });
                });
                
            } catch (error) {
                log(`❌ 解析测试失败: ${error.message}`);
            }
        }

        function testButtonClick() {
            log('=== 测试按钮点击 ===');
            
            try {
                // 创建一个测试按钮
                const testBtn = document.createElement('button');
                testBtn.id = 'test-generate-btn';
                testBtn.textContent = '测试按钮';
                
                let clicked = false;
                testBtn.addEventListener('click', function() {
                    clicked = true;
                    log('✅ 按钮点击事件触发成功');
                });
                
                // 模拟点击
                testBtn.click();
                
                if (clicked) {
                    log('✅ 事件监听器工作正常');
                } else {
                    log('❌ 事件监听器未触发');
                }
                
            } catch (error) {
                log(`❌ 按钮测试失败: ${error.message}`);
            }
        }

        // 自动运行测试
        setTimeout(() => {
            log('开始自动测试...');
            testGenerateFunction();
            setTimeout(() => testParseFunction(), 500);
            setTimeout(() => testButtonClick(), 1000);
        }, 500);
    </script>
</body>
</html>
