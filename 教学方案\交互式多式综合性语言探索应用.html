<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>多式综合性语言探索</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700&display=swap" rel="stylesheet">
    <!-- Chosen Palette: Warm Neutrals & Slate Blue. Background: bg-stone-50, Text: text-slate-800, Primary Accent: bg-sky-700, Secondary Accent: bg-amber-500. This palette is professional, calm, and approachable, fitting for an educational tool. -->
    <!-- Application Structure Plan: A non-linear, thematic SPA organized around a sidebar navigation. The structure is designed for exploration rather than linear reading. Key sections are: 1. 欢迎 (Welcome): A hook to introduce the topic. 2. 核心挑战 (The Core Challenge): Combining key difficulties into one interactive section. 3. 语言类型对比 (Language Type Comparison): An interactive chart to visualize abstract data. 4. “句子词”分解器 (Sentence-Word Deconstructor): A 'wow' feature to visualize the core concept of polysynthesis. 5. 文化与语言 (Culture & Language): Interactive cards to explore the deep connection. 6. AI解决方案 (AI-Powered Solutions): A forward-looking view on tech solutions. 7. 未来之路 (The Path Forward): Actionable recommendations. This structure breaks down a dense report into digestible, engaging modules, promoting user understanding and exploration. -->
    <!-- Visualization & Content Choices: 
        - Report Info: Table 1 (Language Types) -> Goal: Compare -> Viz: Interactive Bar Chart -> Interaction: Click bar to show details -> Justification: Visualizes quantitative ratios better than a table, click interaction makes it engaging. -> Library: Chart.js.
        - Report Info: Table 2 (Polysynthetic Word Examples) -> Goal: Explain -> Viz: Custom HTML/CSS "Deconstructor" diagram -> Interaction: Hover/click morpheme blocks to see definitions -> Justification: Makes the abstract concept of word-building tangible and clear. -> Library/Method: Vanilla JS + Tailwind.
        - Report Info: Table 3 (Cultural Concepts) -> Goal: Explore Relationships -> Viz: Interactive Card Grid -> Interaction: Click card to reveal full details -> Justification: Organizes rich content neatly and encourages exploration. -> Library/Method: Vanilla JS + Tailwind.
        - Report Info: Section IV (AI/Tech) -> Goal: Inform -> Viz: Icon-based feature grid -> Interaction: None, simple display -> Justification: Quickly communicates key technological applications in a visually appealing way. -> Library/Method: Tailwind.
    -->
    <!-- CONFIRMATION: NO SVG graphics used. NO Mermaid JS used. -->
    <style>
        body { font-family: 'Noto Sans SC', sans-serif; }
        .chart-container { position: relative; width: 100%; max-width: 800px; margin-left: auto; margin-right: auto; height: 300px; max-height: 40vh; }
        @media (min-width: 768px) { .chart-container { height: 400px; } }
        .nav-link { transition: all 0.3s ease; }
        .nav-link.active { background-color: #0369a1; color: white; }
        .nav-link:not(.active):hover { background-color: #f1f5f9; color: #0c4a6e; }
        .content-section { display: none; }
        .content-section.active { display: block; }
        .morpheme-block { transition: all 0.3s ease; transform-origin: center; }
        .morpheme-block:hover { transform: scale(1.05); box-shadow: 0 4px 15px rgba(0,0,0,0.1); }
    </style>
</head>
<body class="bg-stone-50 text-slate-800">

    <div class="flex flex-col md:flex-row min-h-screen">
        <!-- Sidebar Navigation -->
        <aside class="w-full md:w-64 bg-white border-b md:border-r border-stone-200 p-4 md:p-6 flex-shrink-0">
            <h1 class="text-2xl font-bold text-sky-800 mb-6">语言迷宫导航</h1>
            <nav id="main-nav" class="flex flex-row md:flex-col gap-2">
                <a href="#welcome" class="nav-link text-slate-600 font-medium px-4 py-2 rounded-lg text-left">欢迎</a>
                <a href="#challenge" class="nav-link text-slate-600 font-medium px-4 py-2 rounded-lg text-left">核心挑战</a>
                <a href="#comparison" class="nav-link text-slate-600 font-medium px-4 py-2 rounded-lg text-left">语言类型对比</a>
                <a href="#deconstructor" class="nav-link text-slate-600 font-medium px-4 py-2 rounded-lg text-left">“句子词”分解器</a>
                <a href="#culture" class="nav-link text-slate-600 font-medium px-4 py-2 rounded-lg text-left">文化与语言</a>
                <a href="#solution" class="nav-link text-slate-600 font-medium px-4 py-2 rounded-lg text-left">AI解决方案</a>
                <a href="#forward" class="nav-link text-slate-600 font-medium px-4 py-2 rounded-lg text-left">未来之路</a>
            </nav>
        </aside>

        <!-- Main Content -->
        <main class="flex-1 p-4 sm:p-6 md:p-10">
            <!-- Welcome Section -->
            <section id="welcome" class="content-section">
                <div class="bg-white p-8 rounded-xl shadow-sm">
                    <h2 class="text-3xl font-bold text-slate-900 mb-4">欢迎来到多式综合性语言的世界</h2>
                    <p class="text-lg text-slate-600 mb-4">
                        您是否想过，一个单词如何能表达一整个句子的意思？欢迎探索多式综合性语言的迷人领域。这些语言以其惊人的形态复杂度著称，但也为学习者、教育者和技术专家带来了独特的挑战。
                    </p>
                    <p class="text-slate-600">
                        本应用旨在将一份深入的学术研究报告，转化为一次引人入胜的互动体验。在这里，您将通过可视化的方式，直观地理解这些语言的结构，探索其背后的文化，并了解人工智能技术如何帮助我们更好地学习和保护这些珍贵的语言遗产。请使用左侧导航开始您的探索之旅。
                    </p>
                </div>
            </section>

            <!-- Core Challenge Section -->
            <section id="challenge" class="content-section">
                <div class="bg-white p-8 rounded-xl shadow-sm">
                    <h2 class="text-3xl font-bold text-slate-900 mb-2">学习的迷宫：核心挑战</h2>
                     <p class="text-slate-500 mb-6">学习多式综合性语言，如同进入一座精巧的迷宫。挑战不仅在于语言本身，更在于学习者的认知过程和文化理解。</p>
                    <div class="grid md:grid-cols-2 gap-6">
                        <div class="bg-stone-100 p-6 rounded-lg">
                            <h3 class="text-xl font-bold text-sky-800 mb-2">形态与句法复杂度</h3>
                            <p class="text-slate-700">一个词即一个句子（“句子词”）是其核心特征。词汇边界模糊，词形变化繁多，给学习者带来巨大的记忆和解析压力。传统的句法规则在这里似乎失效了。</p>
                        </div>
                        <div class="bg-stone-100 p-6 rounded-lg">
                            <h3 class="text-xl font-bold text-sky-800 mb-2">认知负荷</h3>
                            <p class="text-slate-700">对于习惯了分析型语言（如英语、汉语）的成人学习者，处理高度复杂的词语结构会使其工作记忆不堪重负，从而阻碍高效的语言加工和习得。</p>
                        </div>
                        <div class="bg-stone-100 p-6 rounded-lg">
                            <h3 class="text-xl font-bold text-sky-800 mb-2">母语迁移</h3>
                            <p class="text-slate-700">学习者会无意识地将母语的语法习惯带入新语言的学习中。当母语与目标语言类型差异巨大时，这种“负迁移”会成为根深蒂固的障碍。</p>
                        </div>
                        <div class="bg-stone-100 p-6 rounded-lg">
                            <h3 class="text-xl font-bold text-sky-800 mb-2">文化概念习得</h3>
                            <p class="text-slate-700">语言是文化的载体。多式综合性语言中大量词素和语法结构深植于其独特的文化和世界观，无法简单地进行词汇翻译，需要深度的文化理解。</p>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Comparison Section -->
            <section id="comparison" class="content-section">
                <div class="bg-white p-8 rounded-xl shadow-sm">
                    <h2 class="text-3xl font-bold text-slate-900 mb-2">语言世界的光谱：类型对比</h2>
                    <p class="text-slate-500 mb-6">语言的形态并非千篇一律。通过对比“语素/词语之比”，可以直观地看到多式综合性语言在语言类型学光谱中的独特位置。请点击图表中的柱状图进行互动。</p>
                    <div class="chart-container">
                        <canvas id="languageTypeChart"></canvas>
                    </div>
                    <div id="comparison-details" class="mt-8 bg-stone-100 p-6 rounded-lg min-h-[150px]">
                        <h3 id="details-title" class="text-xl font-bold text-sky-800 mb-2">请选择一个语言类型查看详情</h3>
                        <p id="details-text" class="text-slate-700"></p>
                    </div>
                </div>
            </section>

            <!-- Deconstructor Section -->
            <section id="deconstructor" class="content-section">
                 <div class="bg-white p-8 rounded-xl shadow-sm">
                    <h2 class="text-3xl font-bold text-slate-900 mb-2">“句子词”的奥秘：交互式分解器</h2>
                    <p class="text-slate-500 mb-6">体验多式综合性语言最核心的魅力：一个词如何讲述一个完整的故事。选择一个例子，然后将鼠标悬停在下方的语素模块上，查看其含义。</p>
                    <div class="mb-6">
                        <select id="deconstructor-select" class="w-full md:w-1/2 p-2 border border-stone-300 rounded-lg bg-white">
                        </select>
                    </div>
                    <div id="deconstructor-output" class="p-6 bg-stone-100 rounded-lg">
                        <h3 id="decon-word" class="text-2xl font-bold text-center text-sky-800 mb-4 break-all"></h3>
                        <p id="decon-translation" class="text-center text-slate-600 font-medium mb-6"></p>
                        <div id="decon-morphemes" class="flex flex-wrap justify-center items-center gap-2"></div>
                    </div>
                 </div>
            </section>

            <!-- Culture Section -->
            <section id="culture" class="content-section">
                <div class="bg-white p-8 rounded-xl shadow-sm">
                    <h2 class="text-3xl font-bold text-slate-900 mb-2">语言的灵魂：文化概念的嵌入</h2>
                    <p class="text-slate-500 mb-6">在多式综合性语言中，语法和词汇不仅仅是规则，更是世界观的体现。点击下方的卡片，探索语言特征背后深藏的文化内涵。</p>
                    <div id="culture-grid" class="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                    </div>
                </div>
            </section>
            
            <!-- Solution Section -->
            <section id="solution" class="content-section">
                <div class="bg-white p-8 rounded-xl shadow-sm">
                    <h2 class="text-3xl font-bold text-slate-900 mb-2">科技的赋能：AI解决方案</h2>
                    <p class="text-slate-500 mb-6">面对学习的挑战，人工智能和自然语言处理技术为我们提供了前所未有的机遇，特别是在低资源语言的教学和保护中。</p>
                    <div class="grid md:grid-cols-2 gap-6">
                        <div class="bg-stone-100 p-6 rounded-lg flex items-start gap-4">
                             <div class="text-3xl text-sky-700 flex-shrink-0 mt-1">🧠</div>
                            <div>
                                <h3 class="text-xl font-bold text-sky-800 mb-2">AI驱动的形态分析</h3>
                                <p class="text-slate-700">利用NLP技术自动将复杂的“句子词”分解为语素，并解释其功能，让学习者直观理解词语的构成，极大降低学习难度。</p>
                            </div>
                        </div>
                         <div class="bg-stone-100 p-6 rounded-lg flex items-start gap-4">
                            <div class="text-3xl text-sky-700 flex-shrink-0 mt-1">🗣️</div>
                            <div>
                                <h3 class="text-xl font-bold text-sky-800 mb-2">自适应反馈系统</h3>
                                <p class="text-slate-700">通过AI实时评估学习者的发音和语法准确性，提供个性化、即时的纠正建议，并根据学习者的水平动态调整难度。</p>
                            </div>
                        </div>
                        <div class="bg-stone-100 p-6 rounded-lg flex items-start gap-4">
                             <div class="text-3xl text-sky-700 flex-shrink-0 mt-1">💬</div>
                            <div>
                                <h3 class="text-xl font-bold text-sky-800 mb-2">智能辅导系统（ITS）</h3>
                                <p class="text-slate-700">AI聊天机器人可以模拟真实的对话伙伴，提供低压力的会话练习机会。ITS能够创造沉浸式场景，在语境中教授语法和文化。</p>
                            </div>
                        </div>
                        <div class="bg-stone-100 p-6 rounded-lg flex items-start gap-4">
                            <div class="text-3xl text-sky-700 flex-shrink-0 mt-1">🤝</div>
                            <div>
                                <h3 class="text-xl font-bold text-sky-800 mb-2">社区协作与数据增强</h3>
                                <p class="text-slate-700">AI可以从有限的数据中学习，并通过合成数据等方式扩充语料库。最重要的是，技术应与原住民社区紧密合作，确保文化准确性和数据主权。</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Forward Section -->
            <section id="forward" class="content-section">
                 <div class="bg-white p-8 rounded-xl shadow-sm">
                    <h2 class="text-3xl font-bold text-slate-900 mb-2">未来之路：建议与展望</h2>
                    <p class="text-slate-500 mb-6">保护和振兴多式综合性语言需要教育工作者、开发者和政策制定者的共同努力。</p>
                    <div class="grid lg:grid-cols-3 gap-6">
                        <div class="border-t-4 border-sky-600 bg-stone-100 p-6 rounded-lg">
                            <h3 class="text-xl font-bold text-sky-800 mb-3">致教育工作者</h3>
                            <ul class="list-disc list-inside space-y-2 text-slate-700">
                                <li>采用对比分析和支架式教学法。</li>
                                <li>将文化教育融入语言课程的核心。</li>
                                <li>利用可视化工具分解复杂语法。</li>
                                <li>营造低焦虑、鼓励尝试的学习环境。</li>
                            </ul>
                        </div>
                        <div class="border-t-4 border-amber-500 bg-stone-100 p-6 rounded-lg">
                            <h3 class="text-xl font-bold text-amber-700 mb-3">致开发者</h3>
                            <ul class="list-disc list-inside space-y-2 text-slate-700">
                                <li>优先开发AI驱动的形态分析工具。</li>
                                <li>创建自适应、个性化的反馈系统。</li>
                                <li>与原住民社区紧密合作，尊重数据主权。</li>
                                <li>开发沉浸式、游戏化的语境练习场景。</li>
                            </ul>
                        </div>
                        <div class="border-t-4 border-emerald-600 bg-stone-100 p-6 rounded-lg">
                            <h3 class="text-xl font-bold text-emerald-800 mb-3">致政策制定者</h3>
                            <ul class="list-disc list-inside space-y-2 text-slate-700">
                                <li>增加对低资源语言技术研发的资金支持。</li>
                                <li>建立伦理AI开发的框架和指导方针。</li>
                                <li>支持代际语言传承项目。</li>
                                <li>投资原住民社区的数字基础设施和培训。</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </section>
        </main>
    </div>

<script>
document.addEventListener('DOMContentLoaded', () => {

    const appData = {
        comparisonData: {
            labels: ['分析型', '黏着型', '屈折型', '多式综合性'],
            data: [1.1, 1.8, 1.7, 3.0], // Example ratios
            details: [
                { title: '分析型语言 (Analytic)', text: '语素与词语之比较低，主要依靠词序和助词来表达语法关系。每个词通常只承载一个独立的信息单位。典型例子：英语、汉语普通话。' },
                { title: '黏着型语言 (Agglutinative)', text: '使用清晰、独立的语素来表示每个语法功能，即使组合成词后，每个语素仍可被单独识别。典型例子：芬兰语、土耳其语、日语。' },
                { title: '屈折型语言 (Fusional)', text: '将多个语法意义融合到单个语素中，其中各个意义通常难以分离。典型例子：拉丁语、西班牙语。' },
                { title: '多式综合性语言 (Polysynthetic)', text: '语素与词语之比非常高，通过名词并入、多语素复合等方式形成“句子词”，单个词可表达整句意义。典型例子：因纽特语、莫霍克语。' }
            ]
        },
        deconstructorData: [
            {
                word: 'tuntussuqatarniksaitengqiggtuq',
                translation: '他说他不会再去猎杀驯鹿了。',
                morphemes: [
                    { part: 'tuntu', meaning: '驯鹿' },
                    { part: '-ssur-', meaning: '打猎' },
                    { part: '-qatar-', meaning: '将来' },
                    { part: '-ni-', meaning: '说' },
                    { part: '-ksaite-', meaning: '否定' },
                    { part: '-ngqiggte-', meaning: '再次' },
                    { part: '-uq', meaning: '第三人称' }
                ]
            },
            {
                word: 'tusaatsiarunnanngittualuujunga',
                translation: '我听得不是很清楚。',
                morphemes: [
                    { part: 'tusaat', meaning: '听' },
                    { part: '-tsiaq-', meaning: '好地' },
                    { part: '-runnaq-', meaning: '能够' },
                    { part: '-nngit-', meaning: '否定' },
                    { part: '-tualuu-', meaning: '非常' },
                    { part: '-ju-', meaning: '指示' },
                    { part: '-junga', meaning: '我' }
                ]
            }
        ],
        cultureData: [
            {
                language: '莫霍克语',
                feature: 'nyaweh（谢谢）的用法',
                concept: '仅用于向造物主或在重要共享时刻表达感谢，反映了深厚的感恩文化，而非日常随意使用。',
                implication: '教学需强调词语的文化语境和使用规范，而非仅提供直译。'
            },
            {
                language: '莫霍克语',
                feature: '无法直接否定未来',
                concept: '反映了“未来超出人类掌控，属于造物主意志”的文化观念，避免对未来做武断预言。',
                implication: '需解释文化信仰如何影响语法结构，引导学习者转变思维模式。'
            },
            {
                language: '因纽特语',
                feature: '多个“雪”的词语',
                concept: '对自然环境的细致观察和分类（如飘落的雪、地上的雪），反映了生存环境对词汇的塑造。',
                implication: '需结合生活方式解释词汇的深层含义，超越简单的“雪”的翻译。'
            },
            {
                language: '因纽特语',
                feature: '包含式/排除式“我们”',
                concept: '对群体成员身份的细致区分，强调说话者与听者的关系，是“我们（包括你）”还是“我们（不包括你）”。',
                implication: '需通过角色扮演和对话练习，掌握人称代词的社会语用学。'
            }
        ]
    };

    // Navigation
    const navLinks = document.querySelectorAll('#main-nav a');
    const contentSections = document.querySelectorAll('.content-section');

    function updateContent(hash) {
        navLinks.forEach(link => {
            link.classList.toggle('active', link.hash === hash);
        });
        contentSections.forEach(section => {
            section.classList.toggle('active', `#${section.id}` === hash);
        });

        if (hash === '#comparison' && !window.languageChart) {
            initComparisonChart();
        }
    }

    navLinks.forEach(link => {
        link.addEventListener('click', (e) => {
            e.preventDefault();
            const hash = e.target.closest('a').hash;
            window.location.hash = hash;
        });
    });

    window.addEventListener('hashchange', () => {
        const hash = window.location.hash || '#welcome';
        updateContent(hash);
    });

    // Initial load
    const initialHash = window.location.hash || '#welcome';
    updateContent(initialHash);

    // Chart.js - Comparison Chart
    function initComparisonChart() {
        const ctx = document.getElementById('languageTypeChart').getContext('2d');
        window.languageChart = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: appData.comparisonData.labels,
                datasets: [{
                    label: '语素/词语之比 (示意)',
                    data: appData.comparisonData.data,
                    backgroundColor: [
                        'rgba(56, 189, 248, 0.6)',
                        'rgba(251, 191, 36, 0.6)',
                        'rgba(249, 115, 22, 0.6)',
                        'rgba(220, 38, 38, 0.6)'
                    ],
                    borderColor: [
                        'rgba(56, 189, 248, 1)',
                        'rgba(251, 191, 36, 1)',
                        'rgba(249, 115, 22, 1)',
                        'rgba(220, 38, 38, 1)'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: '复杂度（示意比率）'
                        }
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return ` ${context.dataset.label}: ${context.raw}`;
                            }
                        }
                    }
                },
                onClick: (event, elements) => {
                    if (elements.length > 0) {
                        const index = elements[0].index;
                        const details = appData.comparisonData.details[index];
                        document.getElementById('details-title').textContent = details.title;
                        document.getElementById('details-text').textContent = details.text;
                    }
                }
            }
        });
    }

    // Deconstructor
    const deconstructorSelect = document.getElementById('deconstructor-select');
    const deconWord = document.getElementById('decon-word');
    const deconTranslation = document.getElementById('decon-translation');
    const deconMorphemes = document.getElementById('decon-morphemes');

    function loadDeconstructor(index) {
        const data = appData.deconstructorData[index];
        deconWord.textContent = data.word;
        deconTranslation.textContent = `"${data.translation}"`;
        deconMorphemes.innerHTML = '';
        data.morphemes.forEach((m, i) => {
            const block = document.createElement('div');
            block.className = 'morpheme-block bg-white p-4 rounded-lg shadow-sm cursor-pointer text-center';
            block.innerHTML = `<span class="font-bold text-lg text-slate-800">${m.part}</span><br><span class="text-sm text-sky-700">${m.meaning}</span>`;
            deconMorphemes.appendChild(block);
            if(i < data.morphemes.length -1) {
                const plus = document.createElement('div');
                plus.className = 'text-2xl text-slate-400 font-light';
                plus.textContent = '+';
                deconMorphemes.appendChild(plus);
            }
        });
    }

    appData.deconstructorData.forEach((item, index) => {
        const option = document.createElement('option');
        option.value = index;
        option.textContent = item.word;
        deconstructorSelect.appendChild(option);
    });

    deconstructorSelect.addEventListener('change', (e) => {
        loadDeconstructor(e.target.value);
    });

    loadDeconstructor(0);
    
    // Culture Grid
    const cultureGrid = document.getElementById('culture-grid');
    appData.cultureData.forEach(item => {
        const card = document.createElement('div');
        card.className = 'bg-stone-100 p-6 rounded-lg border-l-4 border-sky-600';
        card.innerHTML = `
            <h3 class="font-bold text-lg text-slate-800 mb-2">${item.language}: <span class="font-medium">${item.feature}</span></h3>
            <p class="text-slate-600 mb-3"><strong class="text-slate-700">文化概念:</strong> ${item.concept}</p>
            <p class="text-sky-800 bg-sky-100 p-2 rounded"><strong class="font-medium">教学启示:</strong> ${item.implication}</p>
        `;
        cultureGrid.appendChild(card);
    });
});
</script>

</body>
</html>
