<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>最小化PPT生成器测试</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .input-area { margin-bottom: 20px; }
        textarea { width: 100%; height: 200px; padding: 10px; border: 1px solid #ddd; border-radius: 5px; }
        .btn { background: #007bff; color: white; border: none; padding: 10px 20px; margin: 5px; border-radius: 5px; cursor: pointer; }
        .btn:hover { background: #0056b3; }
        .preview { margin-top: 20px; padding: 20px; border: 1px solid #ddd; border-radius: 5px; background: #f9f9f9; }
        .slide { margin-bottom: 20px; padding: 15px; background: white; border-radius: 5px; box-shadow: 0 2px 5px rgba(0,0,0,0.1); }
        .slide-title { font-size: 24px; font-weight: bold; color: #0D47A1; margin-bottom: 10px; }
        .slide-subtitle { font-size: 18px; color: #1976D2; margin-bottom: 15px; }
        .slide-content { font-size: 16px; line-height: 1.6; }
        .bullet-item { margin: 5px 0; padding-left: 20px; position: relative; }
        .bullet-item:before { content: "•"; color: #1976D2; font-weight: bold; position: absolute; left: 0; }
        .status { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
    </style>
</head>
<body>
    <div class="container">
        <h1>最小化PPT生成器测试</h1>
        
        <div class="input-area">
            <label for="outline-input">输入大纲内容：</label>
            <textarea id="outline-input" placeholder="请输入大纲内容，例如：
# 主标题
## 副标题
- 要点1
- 要点2

## 第二页标题
- 内容1
- 内容2"># 数字人文与语言博物馆
## 项目概述
- 保护和传承语言文化遗产
- 利用数字技术记录方言
- 建立多媒体语言资源库

## 技术特点
- AI语音识别与合成
- 3D虚拟展示技术
- 云端数据存储
- 跨平台访问支持</textarea>
        </div>
        
        <div>
            <button id="generate-btn" class="btn">生成幻灯片</button>
            <button id="clear-btn" class="btn">清空内容</button>
        </div>
        
        <div id="status"></div>
        <div id="preview" class="preview" style="display: none;"></div>
    </div>

    <script>
        let slides = [];
        
        function showStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.innerHTML = `<div class="status ${type}">${message}</div>`;
        }
        
        function parseOutline(text) {
            const slides = [];
            const lines = text.split('\n');
            let currentSlide = null;
            
            lines.forEach(line => {
                const trimmed = line.trim();
                
                if (trimmed.startsWith('# ')) {
                    // 新的主标题幻灯片
                    if (currentSlide) slides.push(currentSlide);
                    currentSlide = {
                        type: 'title',
                        title: trimmed.substring(2).trim(),
                        content: []
                    };
                } else if (trimmed.startsWith('## ')) {
                    // 副标题或新的内容幻灯片
                    if (currentSlide && currentSlide.type === 'title') {
                        currentSlide.subtitle = trimmed.substring(3).trim();
                    } else {
                        if (currentSlide) slides.push(currentSlide);
                        currentSlide = {
                            type: 'content',
                            title: trimmed.substring(3).trim(),
                            content: []
                        };
                    }
                } else if (trimmed.startsWith('- ')) {
                    // 列表项
                    if (currentSlide) {
                        currentSlide.content.push({
                            type: 'bullet',
                            text: trimmed.substring(2).trim()
                        });
                    }
                }
            });
            
            if (currentSlide) slides.push(currentSlide);
            return slides;
        }
        
        function renderSlides() {
            const previewDiv = document.getElementById('preview');
            
            if (slides.length === 0) {
                previewDiv.style.display = 'none';
                return;
            }
            
            let html = '';
            slides.forEach((slide, index) => {
                html += `<div class="slide">`;
                html += `<div class="slide-title">${slide.title}</div>`;
                
                if (slide.subtitle) {
                    html += `<div class="slide-subtitle">${slide.subtitle}</div>`;
                }
                
                if (slide.content && slide.content.length > 0) {
                    html += `<div class="slide-content">`;
                    slide.content.forEach(item => {
                        if (item.type === 'bullet') {
                            html += `<div class="bullet-item">${item.text}</div>`;
                        }
                    });
                    html += `</div>`;
                }
                
                html += `</div>`;
            });
            
            previewDiv.innerHTML = html;
            previewDiv.style.display = 'block';
        }
        
        function generateSlides() {
            const input = document.getElementById('outline-input').value.trim();
            
            if (!input) {
                showStatus('请输入大纲内容', 'error');
                return;
            }
            
            try {
                showStatus('正在生成幻灯片...', 'info');
                
                slides = parseOutline(input);
                
                if (slides.length === 0) {
                    showStatus('未能解析出有效的幻灯片内容，请检查格式', 'error');
                    return;
                }
                
                renderSlides();
                showStatus(`✅ 成功生成 ${slides.length} 张幻灯片！`, 'success');
                
            } catch (error) {
                showStatus(`❌ 生成失败: ${error.message}`, 'error');
                console.error('生成幻灯片时出错:', error);
            }
        }
        
        function clearContent() {
            document.getElementById('outline-input').value = '';
            slides = [];
            document.getElementById('preview').style.display = 'none';
            document.getElementById('status').innerHTML = '';
        }
        
        // 绑定事件
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('generate-btn').addEventListener('click', generateSlides);
            document.getElementById('clear-btn').addEventListener('click', clearContent);
            
            // 自动生成初始内容
            setTimeout(() => {
                generateSlides();
            }, 500);
        });
    </script>
</body>
</html>
