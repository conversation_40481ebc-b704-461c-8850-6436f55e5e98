# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a **Digital Language Museum** project focused on preserving linguistic heritage through multimodal technology. The repository contains educational content and tools for developing language learning applications, particularly centered around Cantonese (围头话) and multilingual emotion analysis.

## Key Components

### 1. HTML-to-PowerPoint Conversion System
- **Main Script**: `convert.py` - A comprehensive Python script that converts HTML presentations to PowerPoint format
- **Input**: `Trial Lesson.html` - HTML-based presentation about Digital Language Museums
- **Output**: Generates PowerPoint files with proper formatting, images, and text

### 2. Interactive PPT Generator
- **Component**: `ppt_generator.tsx` - React-based web application for creating intelligent presentations
- **Features**: 
  - Supports Markdown table parsing and intelligent template selection
  - Includes emotion-aware slide templates for comparison, timeline, and process layouts
  - Exports to HTML and PPTX-compatible formats

### 3. Educational Content
- **Curriculum**: Located in `教学方案/multimodal-language-curriculum.md`
- **Focus**: 22-hour teaching plan for multimodal language technology education
- **Target**: High school students learning about AI, linguistics, and cultural preservation

## Common Development Tasks

### Running the HTML-to-PPT Converter
```bash
python convert.py
```
**Requirements**: 
- `requests`, `beautifulsoup4`, `python-pptx`, `Pillow`
- Input HTML file must be named "Trial Lesson.html"

### Working with the React PPT Generator
The TSX component can be integrated into a React application:
- Uses Lucide React icons for UI elements
- Supports intelligent content parsing from Markdown-like text input
- Includes table recognition and comparison layout generation

### Image Processing Notes
- The converter includes robust image validation using Pillow
- Downloads images from URLs with proper error handling
- Validates image format before adding to slides

## Architecture Notes

### HTML Processing Pipeline
1. **Parse HTML**: Uses BeautifulSoup to extract slide content from HTML sections with class `pdf-slide`
2. **Content Analysis**: Identifies slide types (title, content, comparison, etc.)
3. **Layout Assignment**: Maps content to appropriate PowerPoint slide layouts
4. **Media Integration**: Downloads and validates images from URLs
5. **Export**: Generates final PowerPoint presentation

### React Component Structure
- **State Management**: Uses React hooks for outline content, theme settings, and slide generation
- **Content Parser**: Intelligent markdown parsing with table support
- **Template Engine**: Auto-selects appropriate slide templates based on content analysis
- **Export System**: Supports HTML and JSON export formats

## Cultural Context

This project specifically focuses on:
- **Cantonese Language Preservation**: Tools and content for documenting 围头话 (Weitou dialect)
- **Multilingual Emotion Analysis**: Cross-cultural sentiment analysis including French, Spanish, and Chinese
- **Digital Humanities**: Bridging technology and linguistic heritage preservation
- **Educational Technology**: Age-appropriate learning tools for South Asian children in Hong Kong

## File Dependencies

### Python Scripts
- `convert.py` requires: `requests`, `io`, `sys`, `bs4`, `python-pptx`, `PIL`
- Input dependency: `Trial Lesson.html` must exist in the same directory

### React Components
- `ppt_generator.tsx` requires: `react`, `lucide-react`
- Uses Tailwind CSS for styling
- Supports modern ES6+ features

## Development Guidelines

1. **Language Sensitivity**: Be mindful of cultural context when working with multilingual content
2. **Error Handling**: Both scripts include comprehensive error handling for network requests and file operations
3. **Accessibility**: The React component includes proper semantic HTML and ARIA considerations
4. **Performance**: Image processing includes timeout and validation to prevent hang-ups

## Output Formats

- **PowerPoint**: `.pptx` files with proper slide formatting and embedded media
- **HTML**: Standalone presentations with CSS styling and responsive design
- **JSON**: Structured data for further processing or API integration

## Special Features

### Intelligent Template Selection
The system automatically detects content patterns:
- **Comparison content** → Two-column layouts
- **Timeline/Process content** → Sequential step layouts  
- **Feature lists** → Pyramid/hierarchy layouts
- **Table data** → Specialized table formatting

### Multilingual Support
- Unicode handling for Chinese characters
- Cross-language emotion mapping
- Cultural context preservation in translations

This repository represents an innovative approach to digital language preservation, combining modern web technologies with educational content development for linguistic heritage conservation.