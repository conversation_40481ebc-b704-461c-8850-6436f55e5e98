<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PPT生成器调试测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        #console-output {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>PPT生成器功能调试测试</h1>
        
        <div class="test-section">
            <div class="test-title">🔧 调试信息</div>
            <div class="status info">
                <strong>测试目的：</strong> 验证PPT生成器的核心功能是否正常工作<br>
                <strong>测试时间：</strong> <span id="test-time"></span><br>
                <strong>浏览器：</strong> <span id="browser-info"></span>
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">🧪 功能测试</div>
            <button class="test-button" onclick="testBasicFunctions()">测试基础功能</button>
            <button class="test-button" onclick="testGenerateSlides()">测试幻灯片生成</button>
            <button class="test-button" onclick="testEventBindings()">测试事件绑定</button>
            <button class="test-button" onclick="clearConsole()">清空控制台</button>
        </div>

        <div class="test-section">
            <div class="test-title">📋 控制台输出</div>
            <div id="console-output">等待测试...</div>
        </div>

        <div class="test-section">
            <div class="test-title">🔗 快速链接</div>
            <a href="ppt_generator.html" target="_blank" class="test-button" style="text-decoration: none; display: inline-block;">打开PPT生成器</a>
            <button class="test-button" onclick="window.location.reload()">刷新页面</button>
        </div>
    </div>

    <script>
        // 初始化页面信息
        document.getElementById('test-time').textContent = new Date().toLocaleString();
        document.getElementById('browser-info').textContent = navigator.userAgent.split(' ').slice(-2).join(' ');

        let consoleOutput = document.getElementById('console-output');
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'error' ? '❌' : type === 'success' ? '✅' : 'ℹ️';
            consoleOutput.textContent += `[${timestamp}] ${prefix} ${message}\n`;
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        }

        function clearConsole() {
            consoleOutput.textContent = '';
            log('控制台已清空');
        }

        function testBasicFunctions() {
            log('开始测试基础功能...');
            
            // 测试DOM元素是否存在
            const testElements = [
                'generate-btn',
                'outline-input', 
                'slides-container',
                'slide-indicator'
            ];
            
            testElements.forEach(id => {
                const element = parent.document.getElementById(id);
                if (element) {
                    log(`✓ 元素 ${id} 存在`, 'success');
                } else {
                    log(`✗ 元素 ${id} 不存在`, 'error');
                }
            });
        }

        function testGenerateSlides() {
            log('测试幻灯片生成功能...');
            
            try {
                // 尝试访问父窗口的函数
                if (typeof parent.generateSlides === 'function') {
                    log('✓ generateSlides 函数存在', 'success');
                    
                    // 设置测试内容
                    const testInput = parent.document.getElementById('outline-input');
                    if (testInput) {
                        testInput.value = `# 测试标题\n## 测试副标题\n- 测试列表项1\n- 测试列表项2`;
                        log('✓ 已设置测试内容', 'success');
                        
                        // 调用生成函数
                        parent.generateSlides();
                        log('✓ 已调用 generateSlides 函数', 'success');
                    } else {
                        log('✗ 找不到输入框', 'error');
                    }
                } else {
                    log('✗ generateSlides 函数不存在', 'error');
                }
            } catch (error) {
                log(`✗ 测试生成功能时出错: ${error.message}`, 'error');
            }
        }

        function testEventBindings() {
            log('测试事件绑定...');
            
            try {
                const generateBtn = parent.document.getElementById('generate-btn');
                if (generateBtn) {
                    log('✓ 生成按钮存在', 'success');
                    
                    // 检查是否有事件监听器
                    const hasListeners = generateBtn.onclick || generateBtn.addEventListener;
                    if (hasListeners) {
                        log('✓ 按钮有事件监听器', 'success');
                    } else {
                        log('✗ 按钮没有事件监听器', 'error');
                    }
                } else {
                    log('✗ 生成按钮不存在', 'error');
                }
            } catch (error) {
                log(`✗ 测试事件绑定时出错: ${error.message}`, 'error');
            }
        }

        // 自动运行基础测试
        setTimeout(() => {
            log('自动运行基础功能测试...');
            testBasicFunctions();
        }, 1000);
    </script>
</body>
</html>
