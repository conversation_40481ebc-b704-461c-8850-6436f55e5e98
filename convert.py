import requests
import io
import sys
from bs4 import BeautifulSoup
from pptx import Presentation
from pptx.util import Inches, Pt
from pptx.dml.color import RGBColor
from pptx.enum.text import PP_ALIGN
from bs4.element import Tag
# --- FIX: Import image validation tools from Pillow ---
try:
    from PIL import Image, UnidentifiedImageError
except ImportError:
    print("Error: The 'Pillow' library is required. Please install it by running: pip install Pillow")
    sys.exit(1)

# --- Configuration & Style Definitions ---
PPT_WIDTH = Inches(13.333)
PPT_HEIGHT = Inches(7.5)
DEEP_BLUE = RGBColor(0, 51, 102)
WARM_ORANGE = RGBColor(255, 102, 0)
TEXT_GRAY = RGBColor(74, 85, 104)
LIGHT_BLUE_BG = RGBColor(235, 248, 255)
LIGHT_ORANGE_BG = RGBColor(255, 244, 230)
FONT_H1 = Pt(54)
FONT_H3 = Pt(44)
FONT_H4 = Pt(24)
FONT_P = Pt(18)
FONT_LARGE_P = Pt(21)

def add_textbox(slide, left, top, width, height, text, font_size=FONT_P, bold=False, color=TEXT_GRAY, align=PP_ALIGN.LEFT):
    """Helper function to add a styled textbox to a slide."""
    textbox = slide.shapes.add_textbox(left, top, width, height)
    text_frame = textbox.text_frame
    text_frame.word_wrap = True
    p = text_frame.paragraphs[0]
    p.text = text
    p.font.name = 'Inter'
    p.font.size = font_size
    p.font.bold = bold
    p.font.color.rgb = color
    p.alignment = align
    return textbox

def add_image_from_url(slide, url, left, top, width):
    """
    Downloads an image from a URL, validates it, and adds it to the slide.
    This version is more robust against invalid image data.
    """
    try:
        print(f"  Downloading image from: {url}")
        response = requests.get(url, timeout=15)
        response.raise_for_status()

        content_type = response.headers.get('Content-Type', '')
        if not content_type.startswith('image/'):
            print(f"  Warning: URL did not return an image. Content-Type: '{content_type}'. Skipping.")
            add_textbox(slide, left, top, width, Inches(1), f"[Invalid content from: {url.split('/')[-1]}]")
            return

        image_stream = io.BytesIO(response.content)

        # --- FIX: Validate the image data with Pillow before passing to pptx ---
        try:
            image = Image.open(image_stream)
            image.verify()
            image_stream.seek(0)
        except (UnidentifiedImageError, IOError):
            print(f"  Warning: Content from URL could not be identified as a valid image. Skipping.")
            add_textbox(slide, left, top, width, Inches(1), f"[Invalid image data from: {url.split('/')[-1]}]")
            return
        # --- End of FIX ---

        slide.shapes.add_picture(image_stream, left, top, width=width)
        print("  Image added successfully.")

    except requests.exceptions.RequestException as e:
        print(f"  Warning: Could not download image from {url}. Error: {e}")
        add_textbox(slide, left, top, width, Inches(1), f"[Image failed to load: {url.split('/')[-1]}]")


def load_html_from_file(filename="Trial Lesson.html"):
    """Reads and returns content from an HTML file."""
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            return f.read()
    except FileNotFoundError:
        print(f"Error: The file '{filename}' was not found.")
        print("Please make sure the HTML file is in the same directory and has the correct name.")
        sys.exit(1)
    except Exception as e:
        print(f"An error occurred while reading the file: {e}")
        sys.exit(1)

# --- Main Script Logic ---
def main():
    html_filename = "Trial Lesson.html"
    html_content = load_html_from_file(html_filename)

    prs = Presentation()
    prs.slide_width = PPT_WIDTH
    prs.slide_height = PPT_HEIGHT
    blank_slide_layout = prs.slide_layouts[6]

    soup = BeautifulSoup(html_content, 'html.parser')
    slides_html = soup.find_all('section', class_='pdf-slide')
    print(f"Found {len(slides_html)} slides in '{html_filename}' to convert.")

    for i, slide_html in enumerate(slides_html):
        slide = prs.slides.add_slide(blank_slide_layout)
        print(f"Processing Slide {i+1}...")

        background = slide.background
        fill = background.fill
        fill.solid()
        fill.fore_color.rgb = RGBColor(255, 255, 255)

        # (The rest of the slide processing logic is identical to the previous version)
        # SLIDE 1: Title Slide
        if i == 0:
            fill.fore_color.rgb = DEEP_BLUE
            h1 = slide_html.find('h1')
            h2 = slide_html.find('h2')
            p_intro = slide_html.find('p', class_='text-xl')
            p_author = slide_html.find('div', class_='text-gray-600')

            if h1:
                add_textbox(slide, Inches(1), Inches(2), Inches(11.33), Inches(1.5), h1.text, FONT_H1, True, DEEP_BLUE, PP_ALIGN.CENTER)
            if h2:
                add_textbox(slide, Inches(1), Inches(3.5), Inches(11.33), Inches(1), h2.text, FONT_H4, True, WARM_ORANGE, PP_ALIGN.CENTER)
            if p_intro:
                add_textbox(slide, Inches(1), Inches(4.5), Inches(11.33), Inches(1), p_intro.text, FONT_P, False, TEXT_GRAY, PP_ALIGN.CENTER)
            if p_author:
                add_textbox(slide, Inches(1), Inches(6), Inches(11.33), Inches(1), p_author.text, Pt(14), False, TEXT_GRAY, PP_ALIGN.CENTER)
            continue

        title = slide_html.find('h3')
        if title:
            add_textbox(slide, Inches(0.5), Inches(0.2), Inches(12.33), Inches(1), title.text, FONT_H3, True, DEEP_BLUE, PP_ALIGN.CENTER)

        # SLIDE 2: Opening Question
        if i == 1:
            text_div = slide_html.find('div', class_='text-left')
            img = slide_html.find('img')
            if text_div:
                tb = add_textbox(slide, Inches(0.5), Inches(2), Inches(6), Inches(4), "", align=PP_ALIGN.LEFT)
                tb.text_frame.text = ""
                for p_tag in text_div.find_all('p'):
                    p = tb.text_frame.add_paragraph()
                    p.font.name = 'Inter'
                    p.font.size = FONT_LARGE_P
                    for content in p_tag.contents:
                        run = p.add_run()
                        run.text = content.text
                        if content.name == 'strong':
                            run.font.bold = True
                            run.font.color.rgb = WARM_ORANGE
                        else:
                            run.font.color.rgb = TEXT_GRAY
            if img and img.get('src'):
                add_image_from_url(slide, img['src'], Inches(7), Inches(2), width=Inches(5.8))
            continue
        
        # ... (rest of the slide conditions from i=2 to i=15 are the same)
        # SLIDE 3: Two Columns with Images
        if i == 2:
            cols = slide_html.find_all('div', class_='grid')[0].find_all('div', recursive=False)
            if len(cols) == 2:
                # Left Column
                h4_left = cols[0].find('h4')
                img_left = cols[0].find('img')
                p_left = cols[0].find('p')
                add_textbox(slide, Inches(1), Inches(1.5), Inches(5), Inches(1), h4_left.text, FONT_H4, True, DEEP_BLUE, PP_ALIGN.CENTER)
                if img_left and img_left.get('src'):
                    add_image_from_url(slide, img_left['src'], Inches(1.5), Inches(2.5), width=Inches(4))
                add_textbox(slide, Inches(1), Inches(4.8), Inches(5), Inches(1), p_left.text, FONT_P, align=PP_ALIGN.CENTER)

                # Right Column
                h4_right = cols[1].find('h4')
                img_right = cols[1].find('img')
                p_right = cols[1].find('p')
                add_textbox(slide, Inches(7.33), Inches(1.5), Inches(5), Inches(1), h4_right.text, FONT_H4, True, WARM_ORANGE, PP_ALIGN.CENTER)
                if img_right and img_right.get('src'):
                    add_image_from_url(slide, img_right['src'], Inches(7.83), Inches(2.5), width=Inches(4))
                add_textbox(slide, Inches(7.33), Inches(4.8), Inches(5), Inches(1), p_right.text, FONT_P, align=PP_ALIGN.CENTER)

            p_footer = slide_html.find('p', class_='mt-12')
            if p_footer:
                add_textbox(slide, Inches(1), Inches(6.5), Inches(11.33), Inches(1), p_footer.text, FONT_LARGE_P, True, TEXT_GRAY, PP_ALIGN.CENTER)
            continue

        # SLIDE 4: Full-width Image with Text Below
        if i == 3:
            img = slide_html.find('img')
            text_divs = slide_html.find('div', class_='grid').find_all('p')
            if img and img.get('src'):
                add_image_from_url(slide, img['src'], Inches(1.5), Inches(1.5), width=Inches(10.33))

            tb = add_textbox(slide, Inches(1.5), Inches(4.8), Inches(10.33), Inches(2), "", align=PP_ALIGN.LEFT)
            tb.text_frame.text = ""
            for p_tag in text_divs:
                p = tb.text_frame.add_paragraph()
                p.font.name = 'Inter'
                p.font.size = FONT_P
                if 'text-center' in p_tag.get('class', []):
                     p.alignment = PP_ALIGN.CENTER
                for content in p_tag.contents:
                    run = p.add_run()
                    run.text = content.text
                    if content.name == 'strong':
                        run.font.bold = True
                        run.font.color.rgb = WARM_ORANGE
                    else:
                        run.font.color.rgb = TEXT_GRAY
            continue

        # SLIDE 5: Two Lists
        if i == 4:
            cols = slide_html.find('div', class_='grid').find_all('div', recursive=False)
            if len(cols) == 2:
                h4_left = cols[0].find('h4').text
                list_left = [li.text.strip() for li in cols[0].find_all('li')]
                tb_left = add_textbox(slide, Inches(1), Inches(1.5), Inches(5.5), Inches(5), h4_left + "\n" + "\n".join(list_left))
                para0 = tb_left.text_frame.paragraphs[0]
                para0.font.bold = True
                para0.font.color.rgb = DEEP_BLUE
                para0.font.size = FONT_H4

                h4_right = cols[1].find('h4').text
                list_right = [li.text.strip() for li in cols[1].find_all('li')]
                tb_right = add_textbox(slide, Inches(6.83), Inches(1.5), Inches(5.5), Inches(5), h4_right + "\n" + "\n".join(list_right))
                para0_right = tb_right.text_frame.paragraphs[0]
                para0_right.font.bold = True
                para0_right.font.color.rgb = WARM_ORANGE
                para0_right.font.size = FONT_H4
            continue
        
        # SLIDE 6: Case Study
        if i == 5:
            img = slide_html.find('img')
            text_div = slide_html.find('div', class_='text-left')
            if img and img.get('src'):
                add_image_from_url(slide, img['src'], Inches(0.5), Inches(1.8), width=Inches(6))
            if text_div:
                tb = add_textbox(slide, Inches(7), Inches(2), Inches(5.8), Inches(4), "", align=PP_ALIGN.LEFT)
                tb.text_frame.text = ""
                for p_tag in text_div.find_all('p'):
                    p = tb.text_frame.add_paragraph()
                    p.font.name = 'Inter'
                    p.font.size = FONT_P
                    p.line_spacing = 1.5
                    for content in p_tag.contents:
                        run = p.add_run()
                        run.text = str(content) if content.name != 'strong' else content.text
                        if content.name == 'strong':
                            run.font.bold = True
                            run.font.color.rgb = WARM_ORANGE
                        elif content.name == 'a' and 'href' in content.attrs:
                            run.font.color.rgb = WARM_ORANGE
                            run.hyperlink.address = content['href']
                        else:
                            run.font.color.rgb = TEXT_GRAY
            continue
            # SLIDE 6: Case Study (Image Left, Text Right)
        if i == 5:
            img = slide_html.find('img')
            text_div = slide_html.find('div', class_='text-left')
            if img and img.get('src'):
                add_image_from_url(slide, img['src'], Inches(0.5), Inches(1.8), width=Inches(6))
            if text_div:
                tb = add_textbox(slide, Inches(7), Inches(2), Inches(5.8), Inches(4), "", align=PP_ALIGN.LEFT)
                for p_tag in text_div.find_all('p'):
                    p = tb.text_frame.add_paragraph()
                    p.font.name = 'Inter'
                    p.font.size = FONT_P
                    p.line_spacing = 1.5
                    for content in p_tag.contents:
                        run = p.add_run()
                        run.text = str(content) if content.name != 'strong' else content.text
                        if content.name == 'strong':
                            run.font.bold = True
                            run.font.color.rgb = WARM_ORANGE
                        elif content.name == 'a':
                            run.font.color.rgb = WARM_ORANGE
                            if 'href' in content.attrs:
                                run.hyperlink.address = content['href']
                        else:
                            run.font.color.rgb = TEXT_GRAY
            continue

        # SLIDE 7: Our Vision
        if i == 6:
            p_vision = slide_html.find('p', class_='mb-8')
            if p_vision:
                add_textbox(slide, Inches(0.5), Inches(1.2), Inches(12.33), Inches(1), p_vision.text, FONT_LARGE_P, align=PP_ALIGN.CENTER)

            cols = slide_html.find('div', class_='grid').find_all('div', recursive=False)
            img = slide_html.find('img')

            if len(cols) > 0 and img:
                text_div = cols[0]
                h4 = text_div.find('h4')
                ps = text_div.find_all('p')

                # Text
                tb = add_textbox(slide, Inches(0.5), Inches(2.5), Inches(6), Inches(4), "")
                p_h4 = tb.text_frame.paragraphs[0]
                p_h4.text = h4.text
                p_h4.font.size = FONT_H4
                p_h4.font.bold = True
                p_h4.font.color.rgb = DEEP_BLUE
                for p_tag in ps:
                    p = tb.text_frame.add_paragraph()
                    p.text = p_tag.text
                    p.font.size = FONT_P

                # Image
                if img.get('src'):
                    add_image_from_url(slide, img['src'], Inches(7), Inches(2.2), width=Inches(6))
            continue

        # SLIDE 8: The Power of Context
        if i == 7:
            main_div = slide_html.find('div', class_='max-w-3xl')
            if main_div:
                p1 = main_div.find_all('p')[0]
                p2 = main_div.find_all('p')[1]
                add_textbox(slide, Inches(2), Inches(1.5), Inches(9.33), Inches(1), p1.text, Pt(36), True, TEXT_GRAY, PP_ALIGN.CENTER)
                add_textbox(slide, Inches(2), Inches(2.2), Inches(9.33), Inches(1), p2.text, FONT_P, align=PP_ALIGN.CENTER)

                boxes = main_div.find_all('div', class_='p-4')
                # First box
                p_blue = boxes[0].find('p')
                tb_blue = add_textbox(slide, Inches(3), Inches(3.5), Inches(7.33), Inches(1.2), "", align=PP_ALIGN.LEFT)
                tb_blue.fill.solid()
                tb_blue.fill.fore_color.rgb = LIGHT_BLUE_BG
                run_container = tb_blue.text_frame.paragraphs[0]
                run_container.text = "" # Clear default text
                for content in p_blue.contents:
                    run = run_container.add_run()
                    run.text = content.text.strip()
                    run.font.size = FONT_P
                    if content.name == 'strong':
                        run.font.bold = True
                        run.font.color.rgb = DEEP_BLUE
                    else:
                        run.font.color.rgb = TEXT_GRAY

                # Second box
                p_orange = boxes[1].find('p')
                tb_orange = add_textbox(slide, Inches(3), Inches(4.9), Inches(7.33), Inches(1.2), "", align=PP_ALIGN.LEFT)
                tb_orange.fill.solid()
                tb_orange.fill.fore_color.rgb = LIGHT_ORANGE_BG
                run_container = tb_orange.text_frame.paragraphs[0]
                run_container.text = "" # Clear default text
                for content in p_orange.contents:
                    run = run_container.add_run()
                    run.text = content.text.strip()
                    run.font.size = FONT_P
                    if content.name == 'strong':
                        run.font.bold = True
                        run.font.color.rgb = WARM_ORANGE
                    else:
                        run.font.color.rgb = TEXT_GRAY

                p_footer = main_div.find('p', class_='mt-8')
                if p_footer:
                    add_textbox(slide, Inches(2), Inches(6.5), Inches(9.33), Inches(1), p_footer.text, FONT_P, align=PP_ALIGN.CENTER)
            continue

        # SLIDE 9: Three Pillars of Impact
        if i == 8:
            pillars = slide_html.find_all('div', class_='rounded-full')
            if len(pillars) == 3:
                # Pillar 1
                h4_1 = pillars[0].find('h4').text
                p_1 = pillars[0].find('p').text
                add_textbox(slide, Inches(1.5), Inches(2.5), Inches(3), Inches(2), f"{h4_1}\n\n{p_1}", FONT_P, align=PP_ALIGN.CENTER)

                # Pillar 2
                h4_2 = pillars[1].find('h4').text
                p_2 = pillars[1].find('p').text
                add_textbox(slide, Inches(5.16), Inches(2.2), Inches(3), Inches(3), f"{h4_2}\n\n{p_2}", FONT_P, align=PP_ALIGN.CENTER)
                
                # Pillar 3
                h4_3 = pillars[2].find('h4').text
                p_3 = pillars[2].find('p').text
                add_textbox(slide, Inches(8.8), Inches(2.5), Inches(3), Inches(2), f"{h4_3}\n\n{p_3}", FONT_P, align=PP_ALIGN.CENTER)
            continue
            
        # SLIDE 10: The Journey Ahead
        if i == 9:
            p_intro = slide_html.find('p')
            if p_intro:
                add_textbox(slide, Inches(1), Inches(1.5), Inches(11.33), Inches(1), p_intro.text, FONT_LARGE_P, align=PP_ALIGN.CENTER)

            text_div = slide_html.find('div', class_='text-left')
            if text_div:
                tb = add_textbox(slide, Inches(2.5), Inches(2.5), Inches(8.33), Inches(4), "")
                tb.text_frame.text = "" # Clear default text
                for p_text in text_div.find_all('p'):
                    p = tb.text_frame.add_paragraph()
                    p.font.size = FONT_LARGE_P
                    p.font.name = 'Inter'
                    p.alignment = PP_ALIGN.LEFT
                    p.line_spacing = 1.5
                    strong_tag = p_text.find('strong')
                    if strong_tag:
                        run1 = p.add_run()
                        run1.text = strong_tag.text
                        run1.font.bold = True

                        run2 = p.add_run()
                        run2.text = p_text.text.replace(strong_tag.text, '')
                    else:
                        p.text = p_text.text
            continue

        # SLIDE 11: Icon Grid
        if i == 10:
            p_intro = slide_html.find('p')
            if p_intro:
                add_textbox(slide, Inches(1), Inches(1.5), Inches(11.33), Inches(1), p_intro.text, FONT_LARGE_P, align=PP_ALIGN.CENTER)

            icon_items = slide_html.find_all('div', class_='icon-item')
            # Create a 3x2 grid layout
            rows, cols = 2, 3
            col_width = Inches(3.5)
            row_height = Inches(2)
            start_left = Inches(1.4)
            start_top = Inches(2.8)
            
            for idx, item in enumerate(icon_items):
                row = idx // cols
                col = idx % cols
                left = start_left + col * col_width
                top = start_top + row * row_height
                # We can't render SVGs, so we'll just put the text with a checkmark.
                item_text = item.find('p').text
                # Add a placeholder for icon and then text
                add_textbox(slide, left, top, col_width, row_height, f"✓\n{item_text}", align=PP_ALIGN.CENTER, font_size=FONT_P)
            continue

        # SLIDE 12: First Assignment (Text Left, Image Right)
        if i == 11:
            text_div = slide_html.find('div', class_='text-left')
            img = slide_html.find('img')
            if text_div:
                h4 = text_div.find('h4')
                tb = add_textbox(slide, Inches(0.5), Inches(1.8), Inches(6), Inches(5), "")
                tb.text_frame.text = "" # Clear default
                p_h4 = tb.text_frame.add_paragraph()
                p_h4.text = h4.text
                p_h4.font.name = 'Inter'
                p_h4.font.size = FONT_H4
                p_h4.font.bold = True
                p_h4.font.color.rgb = DEEP_BLUE

                for p_tag in text_div.find_all('p'):
                    p = tb.text_frame.add_paragraph()
                    p.font.size = FONT_P
                    p.line_spacing = 1.2
                    for content in p_tag.contents:
                        run = p.add_run()
                        run.text = content.text
                        if content.name == 'strong':
                            run.font.bold = True
                        if isinstance(content, Tag) and 'text-orange-600' in content.get('class', []):
                            run.font.color.rgb = WARM_ORANGE

            if img and img.get('src'):
                add_image_from_url(slide, img['src'], Inches(7), Inches(2.2), width=Inches(5.8))
            continue

        # SLIDE 13: Process Flow
        if i == 12:
            p_intro = slide_html.find('p')
            if p_intro:
                add_textbox(slide, Inches(1), Inches(1.2), Inches(11.33), Inches(1), p_intro.text, FONT_LARGE_P, align=PP_ALIGN.CENTER)
            
            elements = slide_html.find_all(['div', 'p'], recursive=False)
            if len(elements) > 1:
                items = elements[1].find_all(['div','p'])
                y_pos = 2.0
                for item in items:
                    text = item.get_text(strip=True)
                    if "↓" in text:
                        add_textbox(slide, Inches(1), Inches(y_pos), Inches(11.33), Inches(0.5), text, FONT_H3, True, WARM_ORANGE, PP_ALIGN.CENTER)
                        y_pos += 0.5
                    elif item.name == 'div' and item.has_attr('class') and 'grid' in item['class']:
                        grid_items = item.find_all('div')
                        for j, g_item in enumerate(grid_items):
                            add_textbox(slide, Inches(1.5 + j*3.5), Inches(y_pos), Inches(3.0), Inches(0.8), g_item.text, FONT_P, color=DEEP_BLUE, align=PP_ALIGN.CENTER)
                        y_pos += 1.0
                    else:
                        add_textbox(slide, Inches(1.5), Inches(y_pos), Inches(10.33), Inches(0.8), text, FONT_LARGE_P, align=PP_ALIGN.CENTER)
                        y_pos += 1.0
            else:
                print(f"Warning: Slide {i+1} does not have enough elements for process flow.")
                continue
        
        # SLIDE 14: Checklist
        if i == 13:
            list_items = slide_html.find('ul', class_='checklist').find_all('li')
            if list_items:
                # Create a textbox for the list
                tb = add_textbox(slide, Inches(2.5), Inches(1.8), Inches(8.33), Inches(5), "", align=PP_ALIGN.LEFT)
                tb.text_frame.text = "" # Clear default
                for item in list_items:
                    p = tb.text_frame.add_paragraph()
                    p.font.name = 'Inter'
                    p.font.size = FONT_LARGE_P
                    p.line_spacing = 1.5
                    
                    # Add a colored checkmark run
                    check_run = p.add_run()
                    check_run.text = '✓  '
                    check_run.font.color.rgb = WARM_ORANGE
                    check_run.font.bold = True
                    
                    # Add the rest of the text
                    text_run = p.add_run()
                    text_run.text = item.text.strip()
                    
            continue

        # SLIDE 15: Closing thought
        if i == 14:
            fill.fore_color.rgb = DEEP_BLUE
            h3 = slide_html.find('h3')
            if h3:
                add_textbox(slide, Inches(1), Inches(1), Inches(11.33), Inches(1), h3.text, FONT_H3, True, RGBColor(255,255,255), PP_ALIGN.CENTER)

            text_divs = slide_html.find('div', class_='text-2xl').find_all('p')
            if text_divs:
                tb = add_textbox(slide, Inches(1), Inches(2.5), Inches(11.33), Inches(4), "")
                tb.text_frame.text = "" # Clear default
                
                for p_tag in text_divs:
                    p = tb.text_frame.add_paragraph()
                    p.font.name = 'Inter'
                    p.font.size = FONT_LARGE_P
                    p.alignment = PP_ALIGN.CENTER
                    p.line_spacing = 1.5
                    for content in p_tag.contents:
                        run = p.add_run()
                        run.text = content.text
                        if content.name == 'strong':
                            run.font.bold = True
                            if isinstance(content, Tag) and 'text-orange-600' in content.get('class', []):
                                run.font.color.rgb = WARM_ORANGE
                            else:
                                run.font.color.rgb = DEEP_BLUE
                        else:
                            run.font.color.rgb = RGBColor(255,255,255) # White text for dark background
            continue
        # SLIDE 16: Q&A
        if i == 15:
            img = slide_html.find('img')
            if img and img.get('src'):
                add_image_from_url(slide, img['src'], Inches(3.66), Inches(1.5), width=Inches(6))
            text_div = slide_html.find('div', class_='text-left')
            if text_div:
                content = "\n\n".join([p.text.strip() for p in text_div.find_all('p')])
                add_textbox(slide, Inches(2), Inches(4.5), Inches(9.33), Inches(3), content, FONT_LARGE_P, align=PP_ALIGN.LEFT)
            continue

            # SLIDE 16: Q&A
        if i == 15:
            img = slide_html.find('img')
            if img and img.get('src'):
                add_image_from_url(slide, img['src'], Inches(3.66), Inches(1.5), width=Inches(6))

            text_div = slide_html.find('div', class_='text-left')
            if text_div:
                content = "\n\n".join([p.text.strip() for p in text_div.find_all('p')])
                add_textbox(slide, Inches(2), Inches(4.5), Inches(9.33), Inches(3), content, FONT_LARGE_P, align=PP_ALIGN.LEFT)
            continue
    
    # ... other specific slide handlers ...

    output_filename = "Digital_Language_Museums_Presentation.pptx"
    try:
        prs.save(output_filename)
        print(f"\nPresentation saved successfully as '{output_filename}'")
    except Exception as e:
        print(f"\nError saving presentation: {e}")

if __name__ == "__main__":
    main()
