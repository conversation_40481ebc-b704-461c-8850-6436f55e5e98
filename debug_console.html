<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>调试控制台</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .debug-panel { background: #f8f9fa; border: 1px solid #dee2e6; padding: 20px; border-radius: 8px; margin: 20px 0; }
        .btn { background: #007bff; color: white; border: none; padding: 10px 20px; margin: 5px; border-radius: 5px; cursor: pointer; }
        .btn:hover { background: #0056b3; }
        .console { background: #000; color: #0f0; padding: 15px; border-radius: 5px; font-family: monospace; height: 300px; overflow-y: auto; }
        .error { color: #f00; }
        .success { color: #0f0; }
        .info { color: #00f; }
    </style>
</head>
<body>
    <h1>PPT生成器调试控制台</h1>
    
    <div class="debug-panel">
        <h3>🔧 调试工具</h3>
        <button class="btn" onclick="testMainPage()">测试主页面</button>
        <button class="btn" onclick="checkConsoleErrors()">检查控制台错误</button>
        <button class="btn" onclick="testGenerateFunction()">测试生成函数</button>
        <button class="btn" onclick="clearDebugConsole()">清空控制台</button>
    </div>
    
    <div class="debug-panel">
        <h3>📋 调试输出</h3>
        <div id="debug-console" class="console">等待调试信息...</div>
    </div>
    
    <div class="debug-panel">
        <h3>🔗 快速操作</h3>
        <button class="btn" onclick="openMainPage()">打开主页面</button>
        <button class="btn" onclick="reloadMainPage()">重新加载主页面</button>
    </div>

    <script>
        let debugConsole = document.getElementById('debug-console');
        
        function debugLog(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const className = type;
            debugConsole.innerHTML += `<div class="${className}">[${timestamp}] ${message}</div>`;
            debugConsole.scrollTop = debugConsole.scrollHeight;
        }
        
        function clearDebugConsole() {
            debugConsole.innerHTML = '';
            debugLog('调试控制台已清空');
        }
        
        function testMainPage() {
            debugLog('开始测试主页面...', 'info');
            
            try {
                // 尝试打开主页面并检查
                const testWindow = window.open('ppt_generator.html', '_blank');
                
                setTimeout(() => {
                    try {
                        // 检查页面是否加载
                        if (testWindow && !testWindow.closed) {
                            debugLog('✅ 主页面已打开', 'success');
                            
                            // 检查关键元素
                            const generateBtn = testWindow.document.getElementById('generate-btn');
                            const outlineInput = testWindow.document.getElementById('outline-input');
                            
                            if (generateBtn) {
                                debugLog('✅ 生成按钮存在', 'success');
                            } else {
                                debugLog('❌ 生成按钮不存在', 'error');
                            }
                            
                            if (outlineInput) {
                                debugLog('✅ 输入框存在', 'success');
                            } else {
                                debugLog('❌ 输入框不存在', 'error');
                            }
                            
                            // 检查JavaScript函数
                            if (typeof testWindow.generateSlides === 'function') {
                                debugLog('✅ generateSlides函数存在', 'success');
                            } else {
                                debugLog('❌ generateSlides函数不存在', 'error');
                            }
                            
                        } else {
                            debugLog('❌ 无法打开主页面', 'error');
                        }
                    } catch (error) {
                        debugLog(`❌ 测试主页面时出错: ${error.message}`, 'error');
                    }
                }, 2000);
                
            } catch (error) {
                debugLog(`❌ 打开主页面失败: ${error.message}`, 'error');
            }
        }
        
        function checkConsoleErrors() {
            debugLog('检查控制台错误...', 'info');
            
            // 重写console.error来捕获错误
            const originalError = console.error;
            const errors = [];
            
            console.error = function(...args) {
                errors.push(args.join(' '));
                originalError.apply(console, args);
            };
            
            setTimeout(() => {
                if (errors.length > 0) {
                    debugLog(`发现 ${errors.length} 个控制台错误:`, 'error');
                    errors.forEach(error => {
                        debugLog(`  - ${error}`, 'error');
                    });
                } else {
                    debugLog('✅ 没有发现控制台错误', 'success');
                }
                
                // 恢复原始console.error
                console.error = originalError;
            }, 1000);
        }
        
        function testGenerateFunction() {
            debugLog('测试生成函数...', 'info');
            
            try {
                // 创建测试数据
                const testInput = `# 测试标题
## 测试副标题
- 测试项目1
- 测试项目2`;
                
                debugLog('测试输入:', 'info');
                debugLog(testInput, 'info');
                
                // 简单的解析测试
                const lines = testInput.split('\n');
                let slideCount = 0;
                
                lines.forEach(line => {
                    const trimmed = line.trim();
                    if (trimmed.startsWith('# ')) {
                        slideCount++;
                        debugLog(`发现标题: ${trimmed}`, 'success');
                    }
                });
                
                debugLog(`✅ 解析测试完成，发现 ${slideCount} 个标题`, 'success');
                
            } catch (error) {
                debugLog(`❌ 测试生成函数失败: ${error.message}`, 'error');
            }
        }
        
        function openMainPage() {
            window.open('ppt_generator.html', '_blank');
            debugLog('已打开主页面', 'info');
        }
        
        function reloadMainPage() {
            // 尝试重新加载主页面
            const mainWindow = window.open('ppt_generator.html', '_blank');
            setTimeout(() => {
                if (mainWindow) {
                    mainWindow.location.reload();
                    debugLog('已重新加载主页面', 'info');
                }
            }, 1000);
        }
        
        // 自动运行初始测试
        setTimeout(() => {
            debugLog('=== 自动调试开始 ===', 'info');
            testGenerateFunction();
        }, 500);
    </script>
</body>
</html>
