<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gamma AI - 智能PPT生成器</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #0D47A1 0%, #1976D2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .app-container {
            display: flex;
            height: 100vh;
            background: #f8fafc;
        }

        .sidebar {
            width: 320px;
            background: white;
            border-right: 1px solid #e2e8f0;
            display: flex;
            flex-direction: column;
            box-shadow: 2px 0 10px rgba(0,0,0,0.05);
        }

        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .header {
            background: white;
            border-bottom: 1px solid #e2e8f0;
            padding: 1rem 2rem;
            display: flex;
            justify-content: between;
            align-items: center;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }

        .slide-preview {
            background: white;
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            padding: 24px;
            margin-bottom: 20px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.08);
            aspect-ratio: 16/9;
            display: flex;
            flex-direction: column;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .slide-preview:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.12);
        }

        .slide-preview-single {
            background: white;
            border: 2px solid var(--color-accent-border);
            border-radius: 16px;
            padding: 32px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            min-height: 500px;
            max-height: 80vh;
            overflow-y: auto;
            transition: all 0.3s ease;
        }

        .slide-content-wrapper {
            min-height: 400px;
            display: flex;
            flex-direction: column;
            flex: 1;
        }

        /* AI Features */
        .ai-badge {
            background: linear-gradient(135deg, #0D47A1 0%, #1976D2 100%);
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 10px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .template-card {
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            padding: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
            background: white;
        }

        .template-card:hover {
            border-color: #1976D2;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(25, 118, 210, 0.15);
        }

        .template-card.selected {
            border-color: #1976D2;
            background: linear-gradient(135deg, rgba(13, 71, 161, 0.1) 0%, rgba(25, 118, 210, 0.1) 100%);
        }

        /* Table Styles - Enhanced */
        .table-container {
            width: 100%;
            overflow-x: auto;
            margin-top: 15px;
        }

        .comparison-table, .standard-table {
            width: 100%;
            border-collapse: collapse;
            table-layout: fixed;
            word-wrap: break-word;
            overflow-wrap: break-word;
            border: 1px solid #64B5F6;
            border-radius: 8px;
            overflow: hidden;
        }

        .comparison-table {
            border-spacing: 0;
        }

        .comparison-table th {
            background: linear-gradient(135deg, #0D47A1 0%, #1976D2 100%);
            color: white;
            padding: 12px 16px;
            text-align: center;
            font-weight: 600;
            font-size: 14px;
            border: 1px solid #64B5F6;
            white-space: normal;
            word-wrap: break-word;
            width: auto;
        }

        .comparison-table td {
            background-color: #fafafa;
            padding: 10px 16px;
            font-size: 12px;
            line-height: 1.5;
            border: 1px solid #64B5F6;
            vertical-align: top;
            white-space: normal;
            word-wrap: break-word;
            width: auto;
        }

        .standard-table th {
            background: linear-gradient(135deg, #0D47A1 0%, #1976D2 100%);
            color: white;
            padding: 12px 16px;
            text-align: left;
            font-weight: 600;
            border: 1px solid #64B5F6;
            white-space: normal;
            word-wrap: break-word;
            width: auto;
        }

        .standard-table td {
            padding: 10px 16px;
            border: 1px solid #64B5F6;
            vertical-align: top;
            white-space: normal;
            word-wrap: break-word;
            line-height: 1.4;
            width: auto;
        }

        .standard-table tr:nth-child(even) {
            background-color: #f8fafc;
        }

        .standard-table tr:nth-child(odd) {
            background-color: white;
        }

        /* Modern Button Styles */
        .btn-primary {
            background: linear-gradient(135deg, #0D47A1 0%, #1976D2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(25, 118, 210, 0.3);
        }

        .btn-secondary {
            background: white;
            color: #1976D2;
            border: 2px solid #1976D2;
            padding: 10px 22px;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .btn-secondary:hover {
            background: #1976D2;
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(25, 118, 210, 0.2);
        }

        /* Input Styles */
        .input-field {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            font-size: 14px;
            transition: all 0.3s ease;
            background: white;
        }

        .input-field:focus {
            outline: none;
            border-color: #1976D2;
            box-shadow: 0 0 0 3px rgba(25, 118, 210, 0.1);
        }

        .textarea-field {
            width: 100%;
            padding: 16px;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            font-size: 14px;
            font-family: 'Inter', sans-serif;
            resize: vertical;
            min-height: 200px;
            transition: all 0.3s ease;
            background: white;
        }

        .textarea-field:focus {
            outline: none;
            border-color: #1976D2;
            box-shadow: 0 0 0 3px rgba(25, 118, 210, 0.1);
        }

        /* Loading Animation */
        .loading-spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #1976D2;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Table width distribution options */
        .table-equal-width {
            table-layout: fixed;
        }

        .table-equal-width th,
        .table-equal-width td {
            width: auto;
        }

        .table-auto-width {
            table-layout: auto;
        }
        
        /* Vertical Lists Styles */
        .vertical-lists {
            display: flex;
            flex-direction: column;
            gap: 20px;
            flex: 1;
        }
        
        .list-section {
            flex: 1;
        }
        
        .list-section h3 {
            color: var(--color-primary, #1976D2);
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 12px;
            padding-bottom: 8px;
            border-bottom: 2px solid var(--color-accent-border, #64b5f6);
        }
        
        .ordered-list {
            list-style: none;
            padding-left: 0;
            margin: 0;
        }
        
        .ordered-list > li {
            margin-bottom: 12px;
            position: relative;
            padding-left: 0;
        }
        
        .ordered-list .main-item {
            font-weight: 600;
            font-size: 16px;
            color: var(--color-primary, #1976D2);
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            gap: 12px;
        }
        
        .ordered-list .item-number {
            background-color: var(--color-primary, #1976D2);
            color: white;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: 700;
            flex-shrink: 0;
        }
        
        .ordered-list .sub-items {
            list-style: none;
            padding-left: 36px;
            margin: 0;
        }
        
        .ordered-list .sub-items li {
            margin-bottom: 6px;
            position: relative;
            font-size: 14px;
            color: var(--color-text-dark, #333);
            line-height: 1.5;
        }
        
        .ordered-list .sub-items li:before {
            content: "•";
            color: var(--color-accent, #1976D2);
            font-weight: bold;
            position: absolute;
            left: -16px;
        }
        
        .timeline-container {
            display: flex;
            justify-content: space-around;
            margin-top: 15px;
            position: relative;
        }
        .timeline-container::before {
            content: '';
            position: absolute;
            top: 15px;
            left: 10%;
            right: 10%;
            height: 2px;
            background-color: #64b5f6;
            z-index: 1;
        }
        .timeline-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            flex: 1;
            text-align: center;
            position: relative;
            z-index: 2;
        }
        .timeline-marker {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background-color: #e3f2fd;
            border: 2px solid #64b5f6;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 12px;
            font-weight: 600;
            color: #1565c0;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="app-container">
        <!-- Sidebar -->
        <div class="sidebar">
            <!-- Logo and Header -->
            <div class="p-6 border-b border-gray-200">
                <div class="flex items-center gap-3 mb-4">
                    <div class="w-10 h-10 bg-gradient-to-r from-blue-700 to-blue-500 rounded-lg flex items-center justify-center">
                        <i class="fas fa-magic text-white text-lg"></i>
                    </div>
                    <div>
                        <h1 class="text-xl font-bold text-gray-800">Gamma AI</h1>
                        <p class="text-sm text-gray-500">智能PPT生成器</p>
                    </div>
                </div>
                <div class="ai-badge">
                    <i class="fas fa-robot mr-1"></i>
                    AI 驱动
                </div>
            </div>

            <!-- Input Section -->
            <div class="flex-1 p-6 overflow-y-auto">
                <div class="space-y-6">
                    <!-- Content Input -->
                    <div>
                        <label class="block text-sm font-semibold text-gray-700 mb-3">
                            <i class="fas fa-edit mr-2"></i>
                            演示文稿内容
                        </label>
                        <textarea
                            id="outline-input"
                            class="textarea-field"
                            placeholder="输入您的演示文稿大纲，支持Markdown语法...

示例：
# 数字语言博物馆
## 在数字时代保护语言遗产

# 传统与数字博物馆对比
| 传统博物馆 | 数字语言博物馆 |
| 静态展示 | 互动体验 |
| 有限访问 | 全球覆盖 |

# 核心优势
- 全球可访问性
- 互动学习体验
- 丰富的多媒体文档
- 社区参与"
                        ># 数字语言博物馆
## 在数字时代保护语言遗产

# 传统与数字博物馆对比
| 传统博物馆 | 数字语言博物馆 |
| 静态展示 | 互动体验 |
| 有限访问 | 全球覆盖 |
| 仅观察 | 社区参与 |
| 固定叙述 | 演进故事 |

# 优势与挑战
优势：
- 全球可访问性
- 互动学习体验
- 丰富的多媒体文档
- 社区参与

挑战：
- 技术实施
- 内容创建复杂性
- 用户采用障碍
- 维护要求

# 实施时间线
## 阶段1：规划
初步评估和策略制定

## 阶段2：开发
系统设置和配置

## 阶段3：测试
质量保证和用户验收

## 阶段4：启动
全面部署和上线

# 三大影响支柱

1. 保护 💾
   * 在语言消失前捕获
   * 记录文化知识

2. 教育 🎓
   * 全球可访问性
   * 互动学习

3. 研究 🔬
   * 丰富数据集
   * 计算语言学

# 核心功能
## 功能1：多语言支持
支持濒危语言和方言

## 功能2：互动展览
沉浸式文化体验

## 功能3：社区贡献
用户生成内容和故事</textarea>
                    </div>

                    <!-- AI Template Selection -->
                    <div>
                        <label class="block text-sm font-semibold text-gray-700 mb-3">
                            <i class="fas fa-brain mr-2"></i>
                            AI 模板选择
                        </label>
                        <div class="grid grid-cols-2 gap-2 mb-3">
                            <div class="template-card" data-template="auto">
                                <div class="text-center">
                                    <i class="fas fa-magic text-2xl text-blue-600 mb-2"></i>
                                    <div class="text-xs font-semibold">智能选择</div>
                                </div>
                            </div>
                            <div class="template-card" data-template="cover">
                                <div class="text-center">
                                    <i class="fas fa-file-alt text-2xl text-blue-500 mb-2"></i>
                                    <div class="text-xs font-semibold">封面页</div>
                                </div>
                            </div>
                            <div class="template-card" data-template="content">
                                <div class="text-center">
                                    <i class="fas fa-align-left text-2xl text-green-500 mb-2"></i>
                                    <div class="text-xs font-semibold">标准内容</div>
                                </div>
                            </div>
                            <div class="template-card" data-template="columns">
                                <div class="text-center">
                                    <i class="fas fa-columns text-2xl text-orange-500 mb-2"></i>
                                    <div class="text-xs font-semibold">双栏布局</div>
                                </div>
                            </div>
                            <div class="template-card" data-template="table">
                                <div class="text-center">
                                    <i class="fas fa-table text-2xl text-red-500 mb-2"></i>
                                    <div class="text-xs font-semibold">表格</div>
                                </div>
                            </div>
                            <div class="template-card" data-template="timeline">
                                <div class="text-center">
                                    <i class="fas fa-clock text-2xl text-indigo-500 mb-2"></i>
                                    <div class="text-xs font-semibold">时间线</div>
                                </div>
                            </div>
                        </div>
                        <select id="template-select" class="input-field" style="display: none;">
                            <option value="auto">🤖 智能选择</option>
                            <option value="cover">📄 封面页</option>
                            <option value="content">📝 标准内容</option>
                            <option value="columns">📊 双栏布局</option>
                            <option value="vertical-list">� 垂直列表</option>
                            <option value="table">📋 表格</option>
                            <option value="table-comparison">⚖️ 对比表格</option>
                            <option value="timeline">⏱️ 时间线</option>
                            <option value="pyramid">🔺 流程图</option>
                        </select>
                    </div>

                    <!-- Theme Customization -->
                    <div>
                        <label class="block text-sm font-semibold text-gray-700 mb-3">
                            <i class="fas fa-palette mr-2"></i>
                            主题定制
                        </label>
                        <div class="space-y-3">
                            <div>
                                <label class="text-xs text-gray-600 mb-1 block">主色调</label>
                                <input type="color" id="primary-color" value="#0D47A1" class="input-field h-10">
                            </div>
                            <div>
                                <label class="text-xs text-gray-600 mb-1 block">辅助色</label>
                                <input type="color" id="accent-color" value="#1976D2" class="input-field h-10">
                            </div>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="space-y-3">
                        <button id="generate-btn" class="btn-primary w-full">
                            <i class="fas fa-magic"></i>
                            <span id="generate-text">生成幻灯片</span>
                            <div id="generate-loading" class="loading-spinner" style="display: none;"></div>
                        </button>

                        <div class="grid grid-cols-2 gap-2">
                            <button id="preview-btn" class="btn-secondary">
                                <i class="fas fa-eye"></i>
                                预览
                            </button>
                            <button id="fullscreen-btn" class="btn-secondary">
                                <i class="fas fa-expand"></i>
                                全屏
                            </button>
                        </div>
                    </div>

                    <!-- Export Options -->
                    <div>
                        <label class="block text-sm font-semibold text-gray-700 mb-3">
                            <i class="fas fa-download mr-2"></i>
                            导出选项
                        </label>
                        <div class="space-y-2">
                            <button id="export-html-btn" class="btn-secondary w-full">
                                <i class="fas fa-code"></i>
                                导出 HTML
                            </button>
                            <button id="export-ppt-btn" class="btn-secondary w-full">
                                <i class="fas fa-file-powerpoint"></i>
                                导出 PPT
                            </button>
                            <button id="export-pdf-btn" class="btn-secondary w-full">
                                <i class="fas fa-file-pdf"></i>
                                导出 PDF
                            </button>
                            <button id="export-json-btn" class="btn-secondary w-full">
                                <i class="fas fa-file-code"></i>
                                导出 JSON
                            </button>
                        </div>
                    </div>

                    <!-- Statistics -->
                    <div class="bg-gradient-to-r from-blue-50 to-blue-100 p-4 rounded-lg border border-blue-200">
                        <div class="text-sm font-semibold text-gray-700 mb-2">
                            <i class="fas fa-chart-bar mr-2"></i>
                            生成统计
                        </div>
                        <div id="slide-stats" class="text-xs text-gray-600 space-y-1">
                            <div>📊 <span id="slide-count">0</span> 张幻灯片</div>
                            <div>📋 <span id="table-count">0</span> 个表格</div>
                            <div>🎨 <span id="template-count">0</span> 种模板</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content Area -->
        <div class="main-content">
            <!-- Header -->
            <div class="header">
                <div class="flex items-center justify-between w-full">
                    <div class="flex items-center gap-4">
                        <h2 class="text-xl font-bold text-gray-800">实时预览</h2>
                        <div class="flex items-center gap-2">
                            <div class="w-3 h-3 bg-green-400 rounded-full"></div>
                            <span class="text-sm text-gray-600">AI 已就绪</span>
                        </div>
                    </div>
                    <div class="flex items-center gap-3">
                        <button id="share-btn" class="btn-secondary">
                            <i class="fas fa-share-alt"></i>
                            分享
                        </button>
                        <button id="settings-btn" class="btn-secondary">
                            <i class="fas fa-cog"></i>
                            设置
                        </button>
                    </div>
                </div>
            </div>

            <!-- Preview Area -->
            <div class="flex-1 p-6 overflow-y-auto bg-gray-50">
                <div class="max-w-6xl mx-auto">
                    <!-- Slide Navigation -->
                    <div class="flex items-center justify-between mb-6">
                        <div class="flex items-center gap-4">
                            <button id="prev-slide" class="btn-secondary" disabled>
                                <i class="fas fa-chevron-left"></i>
                            </button>
                            <span id="slide-indicator" class="text-sm text-gray-600">幻灯片 1 / 1</span>
                            <button id="next-slide" class="btn-secondary" disabled>
                                <i class="fas fa-chevron-right"></i>
                            </button>
                        </div>
                        <div class="flex items-center gap-2">
                            <button id="zoom-out" class="btn-secondary">
                                <i class="fas fa-search-minus"></i>
                            </button>
                            <span id="zoom-level" class="text-sm text-gray-600">100%</span>
                            <button id="zoom-in" class="btn-secondary">
                                <i class="fas fa-search-plus"></i>
                            </button>
                        </div>
                    </div>

                    <!-- Slides Container -->
                    <div id="slides-container" class="space-y-6">
                        <!-- Welcome Message -->
                        <div class="text-center py-20">
                            <div class="w-24 h-24 bg-gradient-to-r from-blue-700 to-blue-500 rounded-full flex items-center justify-center mx-auto mb-6">
                                <i class="fas fa-magic text-white text-3xl"></i>
                            </div>
                            <h3 class="text-2xl font-bold text-gray-800 mb-4">欢迎使用 Gamma AI</h3>
                            <p class="text-gray-600 mb-6 max-w-md mx-auto">
                                输入您的内容，我们的AI将自动为您生成专业的演示文稿
                            </p>
                            <button class="btn-primary" onclick="document.getElementById('outline-input').focus()">
                                <i class="fas fa-plus"></i>
                                开始创建
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Fullscreen Modal -->
    <div id="fullscreen-modal" class="fixed inset-0 bg-black bg-opacity-90 z-50 hidden">
        <div class="flex flex-col h-full">
            <!-- Fullscreen Header -->
            <div class="flex items-center justify-between p-4 bg-black bg-opacity-50">
                <div class="flex items-center gap-4">
                    <h3 class="text-white text-lg font-semibold">全屏预览</h3>
                    <div class="flex items-center gap-2">
                        <button id="fs-prev-slide" class="text-white hover:text-gray-300">
                            <i class="fas fa-chevron-left text-xl"></i>
                        </button>
                        <span id="fs-slide-indicator" class="text-white text-sm">1 / 1</span>
                        <button id="fs-next-slide" class="text-white hover:text-gray-300">
                            <i class="fas fa-chevron-right text-xl"></i>
                        </button>
                    </div>
                </div>
                <div class="flex items-center gap-3">
                    <button id="presentation-mode" class="text-white hover:text-gray-300">
                        <i class="fas fa-play text-xl"></i>
                    </button>
                    <button id="close-fullscreen" class="text-white hover:text-gray-300">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>
            </div>

            <!-- Fullscreen Content -->
            <div class="flex-1 flex items-center justify-center p-8">
                <div id="fullscreen-content" class="w-full max-w-6xl">
                    <!-- Fullscreen slides will be shown here -->
                </div>
            </div>
        </div>
    </div>

    <!-- Share Modal -->
    <div id="share-modal" class="fixed inset-0 bg-black bg-opacity-50 z-40 hidden">
        <div class="flex items-center justify-center h-full p-4">
            <div class="bg-white rounded-lg max-w-md w-full p-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold">分享演示文稿</h3>
                    <button id="close-share" class="text-gray-500 hover:text-gray-700">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">分享链接</label>
                        <div class="flex gap-2">
                            <input type="text" id="share-link" class="input-field flex-1" readonly value="https://gamma-ai.example.com/presentation/abc123">
                            <button id="copy-link" class="btn-primary">
                                <i class="fas fa-copy"></i>
                            </button>
                        </div>
                    </div>
                    <div class="grid grid-cols-2 gap-2">
                        <button class="btn-secondary">
                            <i class="fab fa-twitter"></i>
                            Twitter
                        </button>
                        <button class="btn-secondary">
                            <i class="fab fa-linkedin"></i>
                            LinkedIn
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Loading Overlay -->
    <div id="loading-overlay" class="fixed inset-0 bg-white bg-opacity-90 z-30 hidden">
        <div class="flex items-center justify-center h-full">
            <div class="text-center">
                <div class="loading-spinner w-12 h-12 mx-auto mb-4"></div>
                <p class="text-gray-600">AI 正在生成您的演示文稿...</p>
            </div>
        </div>
    </div>

    <!-- Fullscreen Modal -->
    <div id="fullscreen-modal" class="fixed inset-0 bg-black bg-opacity-75 z-50 hidden">
        <div class="flex items-center justify-center h-full p-4">
            <div class="bg-white rounded-lg max-w-4xl w-full max-h-full overflow-auto">
                <div class="p-4 border-b flex justify-between items-center">
                    <h3 class="text-lg font-semibold">Slide Preview</h3>
                    <button id="close-fullscreen" class="text-gray-500 hover:text-gray-700 text-xl">&times;</button>
                </div>
                <div id="fullscreen-content" class="p-6">
                    <!-- Fullscreen slides will be shown here -->
                </div>
            </div>
        </div>
    </div>

    <script>
        // Global variables
        let slides = [];
        let currentSlideIndex = 0;
        let previewVisible = true;
        let zoomLevel = 100;
        let selectedTemplate = 'auto';
        let isGenerating = false;

        let currentTheme = {
            primary: '#0D47A1',
            accent: '#1976D2',
            accentLight: '#E3F2FD',
            accentBorder: '#64B5F6',
            textDark: '#333333',
            textLight: '#757575'
        };

        // Initialize the application
        document.addEventListener('DOMContentLoaded', function() {
            initializeApp();
            bindEvents();
            setupTemplateCards();

            // Close modal when clicking outside
            document.getElementById('fullscreen-modal').addEventListener('click', function(e) {
                if (e.target === this) {
                    closeFullscreen();
                }
            });
        });

        function initializeApp() {
            // Set initial template selection
            document.querySelector('.template-card[data-template="auto"]').classList.add('selected');

            // Initialize theme
            updateThemeVariables();

            // Add sample content for testing
            const sampleContent = `# 数字人文与语言博物馆
## 保护语言文化遗产的数字化解决方案

## 项目背景
- 全球语言多样性面临威胁
- 传统语言保护方法的局限性
- 数字技术为语言保护带来新机遇

## 核心功能
### AI 语言分析
- 自动语音识别和转录
- 语言模式识别
- 方言差异分析

### 数字化存档
- 多媒体内容管理
- 元数据标准化
- 长期保存策略

## 技术优势
- 先进的AI算法
- 云端存储架构
- 用户友好界面
- 多平台兼容

## 应用场景
1. 学术研究
   - 语言学研究
   - 人类学调查
2. 教育培训
   - 语言学习
   - 文化传承
3. 社区参与
   - 本地语言记录
   - 文化活动组织`;

            document.getElementById('outline-input').value = sampleContent;

            // Generate initial slides
            setTimeout(() => {
                generateSlides();
            }, 500);
        }

        function setupTemplateCards() {
            const templateCards = document.querySelectorAll('.template-card');
            templateCards.forEach(card => {
                card.addEventListener('click', function() {
                    // Remove selected class from all cards
                    templateCards.forEach(c => c.classList.remove('selected'));
                    // Add selected class to clicked card
                    this.classList.add('selected');
                    // Update selected template
                    selectedTemplate = this.dataset.template;
                    document.getElementById('template-select').value = selectedTemplate;
                    // Regenerate slides
                    generateSlides();
                });
            });
        }

        function bindEvents() {
            // Main action buttons
            document.getElementById('generate-btn').addEventListener('click', generateSlides);
            document.getElementById('preview-btn').addEventListener('click', togglePreview);

            // Export buttons
            document.getElementById('export-html-btn').addEventListener('click', exportAsHTML);
            document.getElementById('export-ppt-btn').addEventListener('click', exportAsPPT);
            document.getElementById('export-pdf-btn').addEventListener('click', exportAsPDF);
            document.getElementById('export-json-btn').addEventListener('click', exportAsJSON);

            // Navigation buttons
            document.getElementById('prev-slide').addEventListener('click', () => navigateSlide(-1));
            document.getElementById('next-slide').addEventListener('click', () => navigateSlide(1));
            document.getElementById('fs-prev-slide').addEventListener('click', () => navigateSlide(-1));
            document.getElementById('fs-next-slide').addEventListener('click', () => navigateSlide(1));

            // Zoom controls
            document.getElementById('zoom-in').addEventListener('click', () => adjustZoom(10));
            document.getElementById('zoom-out').addEventListener('click', () => adjustZoom(-10));

            // Modal controls
            document.getElementById('fullscreen-btn').addEventListener('click', showFullscreen);
            document.getElementById('close-fullscreen').addEventListener('click', closeFullscreen);
            document.getElementById('share-btn').addEventListener('click', showShareModal);
            document.getElementById('close-share').addEventListener('click', closeShareModal);
            document.getElementById('copy-link').addEventListener('click', copyShareLink);

            // Auto-generate on input change
            document.getElementById('outline-input').addEventListener('input', debounce(generateSlides, 1000));
            document.getElementById('primary-color').addEventListener('change', updateTheme);
            document.getElementById('accent-color').addEventListener('change', updateTheme);

            // Keyboard shortcuts
            document.addEventListener('keydown', handleKeyboardShortcuts);
        }

        function handleKeyboardShortcuts(e) {
            if (e.ctrlKey || e.metaKey) {
                switch(e.key) {
                    case 'Enter':
                        e.preventDefault();
                        generateSlides();
                        break;
                    case 's':
                        e.preventDefault();
                        exportAsHTML();
                        break;
                    case 'f':
                        e.preventDefault();
                        showFullscreen();
                        break;
                }
            } else {
                switch(e.key) {
                    case 'ArrowLeft':
                        if (document.getElementById('fullscreen-modal').style.display !== 'none') {
                            navigateSlide(-1);
                        }
                        break;
                    case 'ArrowRight':
                        if (document.getElementById('fullscreen-modal').style.display !== 'none') {
                            navigateSlide(1);
                        }
                        break;
                    case 'Escape':
                        closeFullscreen();
                        closeShareModal();
                        break;
                }
            }
        }

        function debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }

        function updateTheme() {
            currentTheme.primary = document.getElementById('primary-color').value;
            currentTheme.accent = document.getElementById('accent-color').value;
            currentTheme.accentLight = lightenColor(currentTheme.primary, 0.9);
            currentTheme.accentBorder = lightenColor(currentTheme.accent, 0.3);
            updateThemeVariables();
            generateSlides();
        }

        function updateThemeVariables() {
            const root = document.documentElement;
            root.style.setProperty('--color-primary', currentTheme.primary);
            root.style.setProperty('--color-accent', currentTheme.accent);
            root.style.setProperty('--color-accent-light', currentTheme.accentLight);
            root.style.setProperty('--color-accent-border', currentTheme.accentBorder);
        }

        function navigateSlide(direction) {
            const newIndex = currentSlideIndex + direction;
            if (newIndex >= 0 && newIndex < slides.length) {
                currentSlideIndex = newIndex;
                updateSlideNavigation();
                updateFullscreenSlide();
            }
        }

        function updateSlideNavigation() {
            const indicator = document.getElementById('slide-indicator');
            const fsIndicator = document.getElementById('fs-slide-indicator');
            const prevBtn = document.getElementById('prev-slide');
            const nextBtn = document.getElementById('next-slide');
            const fsPrevBtn = document.getElementById('fs-prev-slide');
            const fsNextBtn = document.getElementById('fs-next-slide');

            const slideText = `幻灯片 ${currentSlideIndex + 1} / ${slides.length}`;
            if (indicator) indicator.textContent = slideText;
            if (fsIndicator) fsIndicator.textContent = slideText;

            if (prevBtn) prevBtn.disabled = currentSlideIndex === 0;
            if (nextBtn) nextBtn.disabled = currentSlideIndex === slides.length - 1;
            if (fsPrevBtn) fsPrevBtn.disabled = currentSlideIndex === 0;
            if (fsNextBtn) fsNextBtn.disabled = currentSlideIndex === slides.length - 1;

            // 更新单页预览
            if (slides.length > 0) {
                renderSingleSlidePreview();
            }
        }

        function adjustZoom(delta) {
            zoomLevel = Math.max(50, Math.min(200, zoomLevel + delta));
            document.getElementById('zoom-level').textContent = `${zoomLevel}%`;

            const container = document.getElementById('slides-container');
            container.style.transform = `scale(${zoomLevel / 100})`;
            container.style.transformOrigin = 'top center';
        }

        function lightenColor(color, percent) {
            const num = parseInt(color.replace("#", ""), 16);
            const amt = Math.round(2.55 * percent * 100);
            const R = (num >> 16) + amt;
            const G = (num >> 8 & 0x00FF) + amt;
            const B = (num & 0x0000FF) + amt;
            return "#" + (0x1000000 + (R < 255 ? R < 1 ? 0 : R : 255) * 0x10000 +
                (G < 255 ? G < 1 ? 0 : G : 255) * 0x100 +
                (B < 255 ? B < 1 ? 0 : B : 255)).toString(16).slice(1);
        }

        // Modal control functions
        function showFullscreen() {
            const modal = document.getElementById('fullscreen-modal');
            modal.classList.remove('hidden');
            updateFullscreenSlide();
            document.body.style.overflow = 'hidden';
        }

        function closeFullscreen() {
            const modal = document.getElementById('fullscreen-modal');
            modal.classList.add('hidden');
            document.body.style.overflow = 'auto';
        }

        function updateFullscreenSlide() {
            const content = document.getElementById('fullscreen-content');
            if (slides.length > 0 && currentSlideIndex < slides.length) {
                const slide = slides[currentSlideIndex];
                content.innerHTML = `
                    <div class="slide-preview" style="width: 100%; height: 70vh; max-width: none;">
                        ${renderSlideContent(slide)}
                    </div>
                `;
            }
        }

        function showShareModal() {
            const modal = document.getElementById('share-modal');
            modal.classList.remove('hidden');
        }

        function closeShareModal() {
            const modal = document.getElementById('share-modal');
            modal.classList.add('hidden');
        }

        function copyShareLink() {
            const linkInput = document.getElementById('share-link');
            linkInput.select();
            document.execCommand('copy');

            const button = document.getElementById('copy-link');
            const originalText = button.innerHTML;
            button.innerHTML = '<i class="fas fa-check"></i>';
            setTimeout(() => {
                button.innerHTML = originalText;
            }, 2000);
        }

        function showLoading() {
            document.getElementById('loading-overlay').classList.remove('hidden');
            document.getElementById('generate-loading').style.display = 'inline-block';
            document.getElementById('generate-text').textContent = '生成中...';
        }

        function hideLoading() {
            document.getElementById('loading-overlay').classList.add('hidden');
            document.getElementById('generate-loading').style.display = 'none';
            document.getElementById('generate-text').textContent = '生成幻灯片';
        }

        function parseOutline(text) {
            console.log('解析大纲文本:', text); // 调试信息

            if (!text || typeof text !== 'string') {
                console.error('无效的输入文本');
                return [];
            }

            const lines = text.split('\n').filter(line => line.trim());
            console.log('处理的行数:', lines.length); // 调试信息

            const slides = [];
            let currentSlide = null;
            let inTable = false;
            let tableHeaders = [];
            let tableRows = [];

            const processTable = () => {
                if (tableHeaders.length > 0 && tableRows.length > 0 && currentSlide) {
                    currentSlide.content.push({
                        type: 'table',
                        headers: tableHeaders,
                        rows: tableRows,
                        isComparison: tableHeaders.length === 2
                    });
                }
                tableHeaders = [];
                tableRows = [];
                inTable = false;
            };

            lines.forEach((line) => {
                const trimmed = line.trim();
                
                // Check if this is a table row
                if (trimmed.startsWith('|') && trimmed.endsWith('|')) {
                    if (!inTable) {
                        inTable = true;
                        tableHeaders = trimmed.split('|')
                            .map(cell => cell.trim())
                            .filter(cell => cell !== '');
                    } else {
                        if (trimmed.includes('-') || trimmed.includes(':')) {
                            return; // Skip separator line
                        }
                        const row = trimmed.split('|')
                            .map(cell => cell.trim())
                            .filter(cell => cell !== '');
                        if (row.length > 0) {
                            tableRows.push(row);
                        }
                    }
                    return;
                } else if (inTable) {
                    processTable();
                }
                
                if (trimmed.startsWith('# ')) {
                    // 一级标题 - 创建新的标题幻灯片
                    if (currentSlide) {
                        if (inTable) processTable();
                        slides.push(currentSlide);
                    }
                    currentSlide = {
                        type: 'title',
                        title: trimmed.substring(2).trim(),
                        subtitle: '',
                        content: [],
                        rawContent: trimmed
                    };
                    console.log('创建标题幻灯片:', currentSlide.title); // 调试信息
                } else if (trimmed.startsWith('## ')) {
                    // 二级标题 - 可能是副标题或新的内容幻灯片
                    if (currentSlide && currentSlide.type === 'title' && !currentSlide.subtitle) {
                        currentSlide.subtitle = trimmed.substring(3).trim();
                        console.log('添加副标题:', currentSlide.subtitle); // 调试信息
                    } else {
                        if (currentSlide) {
                            if (inTable) processTable();
                            slides.push(currentSlide);
                        }
                        currentSlide = {
                            type: 'content',
                            title: trimmed.substring(3).trim(),
                            content: [],
                            rawContent: trimmed
                        };
                        console.log('创建内容幻灯片:', currentSlide.title); // 调试信息
                    }
                } else if (trimmed.startsWith('### ')) {
                    // 三级标题 - 创建内容幻灯片
                    if (currentSlide) {
                        if (inTable) processTable();
                        slides.push(currentSlide);
                    }
                    currentSlide = {
                        type: 'content',
                        title: trimmed.substring(4).trim(),
                        content: [],
                        rawContent: trimmed
                    };
                    console.log('创建三级标题幻灯片:', currentSlide.title); // 调试信息
                } else if (trimmed.match(/^\d+\.\s+/)) {
                    // 有序列表 (1. 2. 3.)
                    if (currentSlide) {
                        const text = trimmed.replace(/^\d+\.\s+/, '').trim();
                        currentSlide.content.push({
                            type: 'ordered',
                            text: text,
                            level: 0
                        });
                        console.log('添加有序列表项:', text); // 调试信息
                    }
                } else if (trimmed.startsWith('- ') || trimmed.startsWith('* ')) {
                    // 无序列表 (- 或 *)
                    if (currentSlide) {
                        const indent = line.search(/\S/); // 找到第一个非空白字符
                        const level = Math.floor(indent / 2); // 每2个空格 = 1级缩进
                        const text = trimmed.substring(2).trim();
                        currentSlide.content.push({
                            type: 'bullet',
                            text: text,
                            level: level
                        });
                        console.log('添加无序列表项:', text, '级别:', level); // 调试信息
                    }
                } else if (trimmed && currentSlide) {
                    // 普通文本
                    currentSlide.content.push({
                        type: 'text',
                        text: trimmed
                    });
                    console.log('添加文本:', trimmed); // 调试信息
                }
            });

            // 处理最后的表格和幻灯片
            if (inTable) processTable();
            if (currentSlide) {
                slides.push(currentSlide);
                console.log('添加最后一张幻灯片:', currentSlide.title); // 调试信息
            }

            console.log('解析完成，共生成', slides.length, '张幻灯片'); // 调试信息

            // 如果没有生成任何幻灯片，创建一个默认的
            if (slides.length === 0 && text.trim()) {
                slides.push({
                    type: 'content',
                    title: '内容',
                    content: [{
                        type: 'text',
                        text: text.trim()
                    }],
                    rawContent: text
                });
                console.log('创建默认幻灯片'); // 调试信息
            }

            return slides;
        }

        function determineSlideTemplate(slide) {
            // Use selected template if not auto
            if (selectedTemplate !== 'auto') return selectedTemplate;

            if (slide.type === 'title') return 'cover';

            const hasTable = slide.content.some(item => item.type === 'table');
            if (hasTable) {
                const table = slide.content.find(item => item.type === 'table');
                if (table.isComparison) {
                    return 'table-comparison';
                }
                return 'table';
            }

            // Enhanced AI template detection
            const hasOrderedList = slide.content.some(item => item.type === 'ordered');
            const hasBulletList = slide.content.some(item => item.type === 'bullet');

            if (hasOrderedList && hasBulletList) {
                const orderedItems = slide.content.filter(item => item.type === 'ordered');
                const bulletItems = slide.content.filter(item => item.type === 'bullet');

                if (orderedItems.length >= 2 && bulletItems.length >= orderedItems.length) {
                    return 'vertical-list';
                }
            }

            const content = slide.content.map(c => c.text || '').join(' ').toLowerCase();
            const title = slide.title.toLowerCase();

            // AI-powered content analysis
            const comparisonKeywords = ['vs', 'versus', '对比', '比较', 'benefits', 'challenges',
                                      'advantages', 'disadvantages', 'pros', 'cons', '优势', '劣势', '挑战'];
            const timelineKeywords = ['timeline', 'process', 'steps', 'phase', 'stage',
                                    '时间线', '流程', '步骤', '阶段', '过程'];
            const featureKeywords = ['features', 'capabilities', 'functions', '功能', '特性', '能力'];

            if (comparisonKeywords.some(keyword => content.includes(keyword) || title.includes(keyword))) {
                return 'columns';
            }

            if (timelineKeywords.some(keyword => content.includes(keyword) || title.includes(keyword))) {
                return 'timeline';
            }

            if (featureKeywords.some(keyword => title.includes(keyword)) || slide.content.length > 6) {
                return 'pyramid';
            }

            return 'content';
        }

        function separateContentForColumns(content) {
            const text = content.map(c => c.text).join(' ');
            
            const comparisonPatterns = [
                { keywords: ['benefits:', 'challenges:'], split: true },
                { keywords: ['advantages:', 'disadvantages:'], split: true },
                { keywords: ['before:', 'after:'], split: true },
                { keywords: ['pros:', 'cons:'], split: true }
            ];
            
            for (let pattern of comparisonPatterns) {
                const [key1, key2] = pattern.keywords;
                if (text.toLowerCase().includes(key1) && text.toLowerCase().includes(key2)) {
                    const col1 = [];
                    const col2 = [];
                    let currentCol = col1;
                    
                    content.forEach(item => {
                        const itemText = item.text.toLowerCase();
                        if (itemText.includes(key1.replace(':', ''))) {
                            currentCol = col1;
                            col1.push({ ...item, text: item.text.replace(/^(benefits?|advantages?|before|pros?):\s*/i, '') });
                        } else if (itemText.includes(key2.replace(':', ''))) {
                            currentCol = col2;
                            col2.push({ ...item, text: item.text.replace(/^(challenges?|disadvantages?|after|cons?):\s*/i, '') });
                        } else {
                            currentCol.push(item);
                        }
                    });
                    
                    return {
                        col1: { title: key1.replace(':', '').charAt(0).toUpperCase() + key1.slice(1, -1), content: col1 },
                        col2: { title: key2.replace(':', '').charAt(0).toUpperCase() + key2.slice(1, -1), content: col2 }
                    };
                }
            }
            
            const mid = Math.ceil(content.length / 2);
            return {
                col1: { title: 'Key Points', content: content.slice(0, mid) },
                col2: { title: 'Additional Details', content: content.slice(mid) }
            };
        }

        function generateSlides() {
            if (isGenerating) return;

            const outline = document.getElementById('outline-input').value.trim();
            console.log('输入内容:', outline); // 调试信息

            if (!outline) {
                slides = [];
                renderPreview();
                updateStats();
                return;
            }

            isGenerating = true;
            showLoading();

            // Simulate AI processing delay
            setTimeout(() => {
                try {
                    console.log('开始解析大纲...'); // 调试信息
                    const parsedSlides = parseOutline(outline);
                    console.log('解析结果:', parsedSlides); // 调试信息

                    if (!parsedSlides || parsedSlides.length === 0) {
                        throw new Error('未能解析出有效的幻灯片内容');
                    }

                    slides = parsedSlides.map((slide, index) => ({
                        ...slide,
                        id: index,
                        template: determineSlideTemplate(slide),
                        separatedContent: slide.content && slide.content.length > 3 ?
                            separateContentForColumns(slide.content.filter(item => item.type !== 'table')) : null
                    }));

                    console.log('生成的幻灯片:', slides); // 调试信息
                    currentSlideIndex = 0;
                    updateStats();
                    updateSlideNavigation();

                    // Show success message
                    if (slides.length > 0) {
                        showNotification(`✅ 成功生成 ${slides.length} 张幻灯片！`, 'success');
                    }
                } catch (error) {
                    console.error('生成幻灯片时出错:', error);
                    showNotification(`❌ 生成幻灯片时出错: ${error.message}`, 'error');
                } finally {
                    isGenerating = false;
                    hideLoading();
                }
            }, 800); // Simulate AI processing time
        }

        function showNotification(message, type = 'info') {
            // Create notification element
            const notification = document.createElement('div');
            notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg transition-all duration-300 ${
                type === 'success' ? 'bg-green-500 text-white' :
                type === 'error' ? 'bg-red-500 text-white' :
                'bg-blue-500 text-white'
            }`;
            notification.textContent = message;

            document.body.appendChild(notification);

            // Auto remove after 3 seconds
            setTimeout(() => {
                notification.style.opacity = '0';
                notification.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    document.body.removeChild(notification);
                }, 300);
            }, 3000);
        }

        function updateStats() {
            document.getElementById('slide-count').textContent = slides.length;
            document.getElementById('table-count').textContent = slides.filter(s => s.content.some(c => c.type === 'table')).length;

            // Count unique templates used
            const uniqueTemplates = new Set(slides.map(s => s.template));
            document.getElementById('template-count').textContent = uniqueTemplates.size;
        }

        function renderPreview() {
            const container = document.getElementById('slides-container');
            if (!container) return;

            if (slides.length === 0) {
                container.innerHTML = `
                    <div class="text-center py-20">
                        <div class="w-24 h-24 bg-gradient-to-r from-blue-700 to-blue-500 rounded-full flex items-center justify-center mx-auto mb-6">
                            <i class="fas fa-magic text-white text-3xl"></i>
                        </div>
                        <h3 class="text-2xl font-bold text-gray-800 mb-4">欢迎使用 Gamma AI</h3>
                        <p class="text-gray-600 mb-6 max-w-md mx-auto">
                            输入您的内容，我们的AI将自动为您生成专业的演示文稿
                        </p>
                        <button class="btn-primary" onclick="document.getElementById('outline-input').focus()">
                            <i class="fas fa-plus"></i>
                            开始创建
                        </button>
                    </div>
                `;
                return;
            }

            container.innerHTML = slides.map((slide, index) => {
                const templateNames = {
                    'cover': '� 封面页',
                    'content': '📝 标准内容',
                    'columns': '📊 双栏布局',
                    'table': '📋 表格',
                    'table-comparison': '⚖️ 对比表格',
                    'vertical-list': '📋 垂直列表',
                    'timeline': '⏱️ 时间线',
                    'pyramid': '🔺 流程图'
                };

                return `
                    <div class="slide-preview ${index === currentSlideIndex ? 'ring-2 ring-blue-500' : ''}"
                         onclick="currentSlideIndex = ${index}; updateSlideNavigation(); renderPreview();">
                        <div class="flex items-center justify-between mb-3">
                            <span class="text-xs font-medium px-3 py-1 bg-gradient-to-r from-blue-100 to-blue-200 text-blue-800 rounded-full">
                                ${templateNames[slide.template] || slide.template}
                            </span>
                            <div class="flex items-center gap-2">
                                <span class="text-xs text-gray-500">第 ${index + 1} 页</span>
                                ${slide.content.some(c => c.type === 'table') ?
                                    '<span class="text-xs bg-green-100 text-green-700 px-2 py-1 rounded-full">📊</span>' : ''}
                            </div>
                        </div>
                        ${renderSlideContent(slide)}
                    </div>
                `;
            }).join('');

            // 使用单页预览模式
            renderSingleSlidePreview();
        }

        // 新的单页预览函数
        function renderSingleSlidePreview() {
            const container = document.getElementById('slides-container');
            if (!container || slides.length === 0) return;

            const currentSlide = slides[currentSlideIndex];
            if (!currentSlide) return;

            const templateNames = {
                'cover': '📄 封面页',
                'content': '📝 标准内容',
                'columns': '📊 双栏布局',
                'table': '📋 表格',
                'table-comparison': '⚖️ 对比表格',
                'vertical-list': '📋 垂直列表',
                'timeline': '⏱️ 时间线',
                'pyramid': '🔺 流程图'
            };

            container.innerHTML = `
                <div class="slide-preview-single">
                    <div class="flex items-center justify-between mb-4">
                        <span class="text-sm font-medium px-4 py-2 bg-gradient-to-r from-blue-100 to-blue-200 text-blue-800 rounded-full">
                            ${templateNames[currentSlide.template] || currentSlide.template}
                        </span>
                        <div class="flex items-center gap-3">
                            <span class="text-sm text-gray-600">第 ${currentSlideIndex + 1} 页 / 共 ${slides.length} 页</span>
                            ${currentSlide.content.some(c => c.type === 'table') ?
                                '<span class="text-sm bg-green-100 text-green-700 px-3 py-1 rounded-full">📊 包含表格</span>' : ''}
                        </div>
                    </div>
                    <div class="slide-content-wrapper">
                        ${renderSlideContent(currentSlide)}
                    </div>
                </div>
            `;
        }

        function renderSlideContent(slide) {
            try {
                switch (slide.template) {
                    case 'cover':
                        return `
                            <div class="text-center py-8">
                                <h1 class="text-3xl font-bold mb-4" style="color: ${currentTheme.primary}">
                                    ${slide.title}
                                </h1>
                                ${slide.subtitle ? `
                                    <p class="text-xl" style="color: ${currentTheme.textLight}">
                                        ${slide.subtitle}
                                    </p>
                                ` : ''}
                            </div>
                        `;

                    case 'table-comparison':
                        const table = slide.content.find(item => item.type === 'table');
                        if (table) {
                            return `
                                <div class="flex-1">
                                    <h2 class="text-xl font-bold mb-4" style="color: ${currentTheme.primary}">
                                        ${slide.title}
                                    </h2>
                                    <div class="table-container">
                                        <table class="comparison-table">
                                            <thead>
                                                <tr>
                                                    ${table.headers.map(header => `<th>${header}</th>`).join('')}
                                                </tr>
                                            </thead>
                                            <tbody>
                                                ${table.rows.map(row => `
                                                    <tr>
                                                        ${row.map(cell => `<td>${cell}</td>`).join('')}
                                                    </tr>
                                                `).join('')}
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            `;
                        }
                        return '<div class="text-red-500">❌ 表格数据未找到</div>';

                    case 'table':
                        const standardTable = slide.content.find(item => item.type === 'table');
                        if (standardTable) {
                            return `
                                <div class="flex-1">
                                    <h2 class="text-xl font-bold mb-4" style="color: ${currentTheme.primary}">
                                        ${slide.title}
                                    </h2>
                                    <div class="table-container">
                                        <table class="standard-table">
                                            <thead>
                                                <tr>
                                                    ${standardTable.headers.map(header => `
                                                        <th style="background-color: ${currentTheme.primary}">
                                                            ${header}
                                                        </th>
                                                    `).join('')}
                                                </tr>
                                            </thead>
                                            <tbody>
                                                ${standardTable.rows.map((row, i) => `
                                                    <tr>
                                                        ${row.map(cell => `<td>${cell}</td>`).join('')}
                                                    </tr>
                                                `).join('')}
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            `;
                        }
                        return '<div class="text-red-500">❌ 表格数据未找到</div>';
                
                case 'columns':
                    const nonTableContent = slide.content.filter(item => item.type !== 'table');
                    const separated = slide.separatedContent || separateContentForColumns(nonTableContent);
                    return `
                        <div class="flex-1">
                            <h2 class="text-lg font-bold mb-3" style="color: ${currentTheme.primary}">
                                ${slide.title}
                            </h2>
                            <div class="grid grid-cols-2 gap-4 flex-1">
                                <div>
                                    <h3 class="font-semibold mb-2" style="color: ${currentTheme.primary}">
                                        ${separated.col1.title}
                                    </h3>
                                    <ul class="text-xs space-y-1">
                                        ${separated.col1.content.map(item => `<li>• ${item.text}</li>`).join('')}
                                    </ul>
                                </div>
                                <div>
                                    <h3 class="font-semibold mb-2" style="color: ${currentTheme.primary}">
                                        ${separated.col2.title}
                                    </h3>
                                    <ul class="text-xs space-y-1">
                                        ${separated.col2.content.map(item => `<li>• ${item.text}</li>`).join('')}
                                    </ul>
                                </div>
                            </div>
                        </div>
                    `;
                
                case 'vertical-list':
                    return renderVerticalList(slide, currentTheme);
                
                case 'timeline':
                    const timelineContent = slide.content.filter(item => item.type !== 'table');
                    return `
                        <div class="flex-1">
                            <h2 class="text-lg font-bold mb-3" style="color: ${currentTheme.primary}">
                                ${slide.title}
                            </h2>
                            <div class="timeline-container flex-1 flex items-center">
                                ${timelineContent.slice(0, 4).map((item, i) => `
                                    <div class="timeline-item">
                                        <div class="timeline-marker">${i + 1}</div>
                                        <div class="text-xs">
                                            <div class="font-semibold">
                                                ${item.text.split(':')[0]}
                                            </div>
                                            <div class="text-gray-600 mt-1">
                                                ${item.text.includes(':') ? item.text.split(':')[1] : ''}
                                            </div>
                                        </div>
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                    `;
                
                default:
                    const contentItems = slide.content.filter(item => item.type !== 'table');
                    const tableItems = slide.content.filter(item => item.type === 'table');
                    return `
                        <div class="flex-1">
                            <h2 class="text-xl font-bold mb-4" style="color: ${currentTheme.primary}">
                                ${slide.title}
                            </h2>
                            <div class="flex-1 overflow-auto">
                                ${renderContentItems(contentItems, currentTheme)}
                                ${tableItems.map(table => `
                                    <div class="table-container mb-4">
                                        <table class="standard-table">
                                            <thead>
                                                <tr>
                                                    ${table.headers.map(header => `
                                                        <th style="background-color: ${currentTheme.primary}">
                                                            ${header}
                                                        </th>
                                                    `).join('')}
                                                </tr>
                                            </thead>
                                            <tbody>
                                                ${table.rows.map((row, j) => `
                                                    <tr>
                                                        ${row.map(cell => `<td>${cell}</td>`).join('')}
                                                    </tr>
                                                `).join('')}
                                            </tbody>
                                        </table>
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                    `;
                }
            } catch (error) {
                console.error('渲染幻灯片内容时出错:', error);
                return `
                    <div class="flex-1 flex items-center justify-center">
                        <div class="text-center text-red-500">
                            <i class="fas fa-exclamation-triangle text-2xl mb-2"></i>
                            <p>渲染幻灯片时出错</p>
                            <p class="text-sm">${error.message}</p>
                        </div>
                    </div>
                `;
            }
        }

        function renderContentItems(items, theme) {
            if (!items || items.length === 0) return '';

            return `
                <ul class="space-y-2">
                    ${items.map(item => {
                        switch(item.type) {
                            case 'bullet':
                                return `<li class="flex items-start">
                                    <span class="text-blue-500 mr-2">•</span>
                                    <span>${item.text}</span>
                                </li>`;
                            case 'ordered':
                                return `<li class="flex items-start">
                                    <span class="text-blue-500 mr-2 font-semibold">${item.number || '•'}</span>
                                    <span>${item.text}</span>
                                </li>`;
                            case 'text':
                                return `<li class="text-gray-700">${item.text}</li>`;
                            default:
                                return `<li>${item.text || ''}</li>`;
                        }
                    }).join('')}
                </ul>
            `;
        }

        function renderVerticalList(slide, theme) {
            // Group ordered items with their sub-bullets
            const groups = [];
            let currentGroup = null;
            
            slide.content.forEach(item => {
                if (item.type === 'ordered') {
                    if (currentGroup) {
                        groups.push(currentGroup);
                    }
                    currentGroup = {
                        title: item.text,
                        items: []
                    };
                } else if (item.type === 'bullet' && currentGroup) {
                    currentGroup.items.push(item.text);
                }
            });
            
            if (currentGroup) {
                groups.push(currentGroup);
            }
            
            return `
                <div class="flex-1">
                    <h2 class="text-lg font-bold mb-3" style="color: ${theme.primary}">
                        ${slide.title}
                    </h2>
                    <div class="vertical-lists">
                        <ol class="ordered-list">
                            ${groups.map((group, index) => `
                                <li>
                                    <div class="main-item">
                                        <div class="item-number" style="background-color: ${theme.primary}">${index + 1}</div>
                                        <span>${group.title}</span>
                                    </div>
                                    ${group.items.length > 0 ? `
                                        <ul class="sub-items">
                                            ${group.items.map(item => `<li>${item}</li>`).join('')}
                                        </ul>
                                    ` : ''}
                                </li>
                            `).join('')}
                        </ol>
                    </div>
                </div>
            `;
        }



        function togglePreview() {
            previewVisible = !previewVisible;
            const mainContent = document.querySelector('.main-content');
            if (previewVisible) {
                mainContent.style.display = 'flex';
                document.getElementById('preview-btn').innerHTML = '<i class="fas fa-eye-slash"></i> 隐藏预览';
            } else {
                mainContent.style.display = 'none';
                document.getElementById('preview-btn').innerHTML = '<i class="fas fa-eye"></i> 显示预览';
            }
        }

        // Enhanced export functions
        function exportAsPPT() {
            if (slides.length === 0) {
                showNotification('❌ 请先生成幻灯片', 'error');
                return;
            }

            showNotification('🔄 正在生成PPT文件...', 'info');

            // Create a simple PPT-like HTML structure
            const pptContent = generatePPTContent();
            const blob = new Blob([pptContent], { type: 'text/html' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'presentation.html';
            a.click();
            URL.revokeObjectURL(url);

            showNotification('✅ PPT文件已导出！', 'success');
        }

        function exportAsPDF() {
            if (slides.length === 0) {
                showNotification('❌ 请先生成幻灯片', 'error');
                return;
            }

            showNotification('🔄 正在生成PDF文件...', 'info');

            // Use html2canvas and jsPDF to generate PDF
            const { jsPDF } = window.jspdf;
            const pdf = new jsPDF('landscape', 'mm', 'a4');

            let currentPage = 0;
            const slideElements = document.querySelectorAll('.slide-preview');

            function processSlide(index) {
                if (index >= slideElements.length) {
                    pdf.save('presentation.pdf');
                    showNotification('✅ PDF文件已导出！', 'success');
                    return;
                }

                if (index > 0) {
                    pdf.addPage();
                }

                html2canvas(slideElements[index], {
                    scale: 2,
                    useCORS: true,
                    allowTaint: true
                }).then(canvas => {
                    const imgData = canvas.toDataURL('image/png');
                    const imgWidth = 297; // A4 landscape width in mm
                    const imgHeight = (canvas.height * imgWidth) / canvas.width;

                    pdf.addImage(imgData, 'PNG', 0, 0, imgWidth, imgHeight);
                    processSlide(index + 1);
                }).catch(error => {
                    console.error('Error generating PDF:', error);
                    showNotification('❌ PDF生成失败', 'error');
                });
            }

            processSlide(0);
        }

        function exportAsHTML() {
            if (slides.length === 0) {
                showNotification('❌ 请先生成幻灯片', 'error');
                return;
            }

            showNotification('🔄 正在生成HTML文件...', 'info');

            const htmlContent = generateHTMLExport();
            const blob = new Blob([htmlContent], { type: 'text/html' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'gamma-presentation.html';
            a.click();
            URL.revokeObjectURL(url);

            showNotification('✅ HTML文件已导出！', 'success');
        }

        function exportAsJSON() {
            if (slides.length === 0) {
                showNotification('❌ 请先生成幻灯片', 'error');
                return;
            }

            const jsonData = {
                title: "Gamma AI 生成的演示文稿",
                created: new Date().toISOString(),
                theme: currentTheme,
                selectedTemplate: selectedTemplate,
                metadata: {
                    slideCount: slides.length,
                    tableCount: slides.filter(s => s.content.some(c => c.type === 'table')).length,
                    templateCount: new Set(slides.map(s => s.template)).size
                },
                slides: slides.map(slide => ({
                    id: slide.id,
                    template: slide.template,
                    title: slide.title,
                    subtitle: slide.subtitle || '',
                    content: slide.content,
                    separatedContent: slide.separatedContent
                }))
            };

            const blob = new Blob([JSON.stringify(jsonData, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'gamma-presentation-data.json';
            a.click();
            URL.revokeObjectURL(url);

            showNotification('✅ JSON文件已导出！', 'success');
        }

        function generatePPTContent() {
            return `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gamma AI 演示文稿</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', 'PingFang SC', sans-serif;
            margin: 0;
            padding: 0;
            background: #f5f5f5;
        }
        .presentation {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .slide {
            background: white;
            border-radius: 12px;
            padding: 40px;
            margin-bottom: 30px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            aspect-ratio: 16/9;
            display: flex;
            flex-direction: column;
        }
        .slide h1 { color: ${currentTheme.primary}; font-size: 2.5em; margin-bottom: 0.5em; }
        .slide h2 { color: ${currentTheme.primary}; font-size: 2em; margin-bottom: 1em; }
        .slide h3 { color: ${currentTheme.accent}; font-size: 1.5em; margin-bottom: 0.5em; }
        .table { width: 100%; border-collapse: collapse; margin: 20px 0; }
        .table th { background: ${currentTheme.primary}; color: white; padding: 12px; }
        .table td { padding: 12px; border: 1px solid #ddd; }
        @media print {
            .slide { page-break-after: always; margin-bottom: 0; }
        }
    </style>
</head>
<body>
    <div class="presentation">
        ${slides.map(slide => `
            <div class="slide">
                ${renderSlideContent(slide)}
            </div>
        `).join('')}
    </div>
</body>
</html>`;
        }

        function generateHTMLExport() {
            const css = `
                <style>
                    :root {
                        --color-primary: ${currentTheme.primary};
                        --color-accent: ${currentTheme.accent};
                        --color-accent-light: ${currentTheme.accentLight};
                        --color-accent-border: ${currentTheme.accentBorder};
                        --color-text-dark: ${currentTheme.textDark};
                        --color-text-light: ${currentTheme.textLight};
                    }
                    body {
                        background-color: #F0F2F5;
                        font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
                        color: var(--color-text-dark);
                        margin: 0;
                        padding: 20px;
                    }
                    .slide {
                        background-color: #ffffff;
                        border: 1px solid #E0E0E0;
                        box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
                        border-radius: 12px;
                        width: 960px;
                        height: 540px;
                        aspect-ratio: 16/9;
                        margin: 25px auto;
                        padding: 40px 60px;
                        box-sizing: border-box;
                        display: flex;
                        flex-direction: column;
                        overflow: hidden;
                    }
                    .table-container { width: 100%; overflow-x: auto; margin-top: 15px; }
                    .comparison-table, .standard-table {
                        width: 100%;
                        border-collapse: collapse;
                        table-layout: fixed;
                        word-wrap: break-word;
                        overflow-wrap: break-word;
                        border: 1px solid var(--color-accent-border);
                        border-radius: 8px;
                        overflow: hidden;
                    }
                    .comparison-table th {
                        background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-accent) 100%);
                        color: white;
                        padding: 12px 16px;
                        text-align: center;
                        font-weight: 600;
                        font-size: 14px;
                        border: 1px solid var(--color-accent-border);
                        white-space: normal;
                        word-wrap: break-word;
                        width: auto;
                    }
                    .comparison-table td {
                        background-color: #fafafa;
                        padding: 10px 16px;
                        font-size: 12px;
                        line-height: 1.5;
                        border: 1px solid var(--color-accent-border);
                        vertical-align: top;
                        white-space: normal;
                        word-wrap: break-word;
                        width: auto;
                    }
                    .standard-table th {
                        background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-accent) 100%);
                        color: white;
                        padding: 12px 16px;
                        text-align: left;
                        font-weight: 600;
                        border: 1px solid var(--color-accent-border);
                        white-space: normal;
                        word-wrap: break-word;
                        width: auto;
                    }
                    .standard-table td {
                        padding: 10px 16px;
                        border: 1px solid var(--color-accent-border);
                        vertical-align: top;
                        white-space: normal;
                        word-wrap: break-word;
                        line-height: 1.4;
                        width: auto;
                    }
                    .standard-table tr:nth-child(even) { background-color: #f9f9f9; }
                    .standard-table tr:nth-child(odd) { background-color: white; }
                    .slide h1 { font-size: 48px; color: var(--color-primary); font-weight: 700; margin: 0; }
                    .slide h2 { font-size: 34px; color: var(--color-primary); font-weight: 700; margin: 0 0 25px 0; padding-bottom: 15px; border-bottom: 3px solid var(--color-accent-border); }
                    .slide h3 { font-size: 24px; color: var(--color-primary); font-weight: 600; margin-bottom: 10px; }
                    .slide p, .slide li { font-size: 16px; line-height: 1.7; color: var(--color-text-dark); }
                    .slide .subtitle { font-size: 24px; font-weight: 300; color: var(--color-text-light); text-align: center; margin-top: 10px; }
                    .layout-center { justify-content: center; align-items: center; text-align: center; }
                    .columns { display: flex; flex-grow: 1; width: 100%; gap: 40px; }
                    .columns .col { flex: 1; }

                    .comparison-table th { background-color: var(--color-accent-light); color: var(--color-primary); padding: 15px 20px; text-align: center; font-weight: 600; border-radius: 8px 8px 0 0; font-size: 18px; }
                    .comparison-table td { background-color: #FAFAFA; padding: 12px 20px; border-left: 3px solid var(--color-accent-border); font-size: 15px; line-height: 1.6; }
                    .timeline { position: relative; display: flex; justify-content: space-around; width: 100%; margin-top: 20px; }
                    .timeline::before { content: ''; position: absolute; top: 20px; left: 10%; right: 10%; height: 2px; background-color: var(--color-accent-border); z-index: 1; }
                    .timeline-item { display: flex; flex-direction: column; align-items: center; flex: 1; text-align: center; position: relative; z-index: 2; }
                    .timeline-marker { width: 40px; height: 40px; border-radius: 50%; background-color: var(--color-accent-light); border: 2px solid var(--color-accent-border); display: flex; justify-content: center; align-items: center; font-size: 18px; font-weight: 600; color: var(--color-accent); margin-bottom: 15px; }
                    .slide ul { list-style-type: none; padding-left: 0; }
                    .slide li { padding: 5px 0; }
                    .slide li:before { content: "•"; color: var(--color-accent); font-weight: bold; display: inline-block; width: 1em; }
                </style>
            `;

            const slidesHTML = slides.map(slide => {
                switch (slide.template) {
                    case 'cover':
                        return `
                            <div class="slide layout-center">
                                <h1>${slide.title}</h1>
                                ${slide.subtitle ? `<p class="subtitle">${slide.subtitle}</p>` : ''}
                            </div>
                        `;
                    
                    case 'table-comparison':
                        const table = slide.content.find(item => item.type === 'table');
                        if (table) {
                            return `
                                <div class="slide">
                                    <h2>${slide.title}</h2>
                                    <div class="table-container">
                                        <table class="comparison-table">
                                            <thead>
                                                <tr>
                                                    ${table.headers.map(header => `<th>${header}</th>`).join('')}
                                                </tr>
                                            </thead>
                                            <tbody>
                                                ${table.rows.map(row => `
                                                    <tr>
                                                        ${row.map(cell => `<td>${cell}</td>`).join('')}
                                                    </tr>
                                                `).join('')}
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            `;
                        }
                        return '<div class="slide"><h2>Error: Table not found</h2></div>';
                    
                    case 'columns':
                        const nonTableContent = slide.content.filter(item => item.type !== 'table');
                        const separated = slide.separatedContent || separateContentForColumns(nonTableContent);
                        return `
                            <div class="slide">
                                <h2>${slide.title}</h2>
                                <div class="columns">
                                    <div class="col">
                                        <h3>${separated.col1.title}</h3>
                                        <ul>
                                            ${separated.col1.content.map(item => `<li>${item.text}</li>`).join('')}
                                        </ul>
                                    </div>
                                    <div class="col">
                                        <h3>${separated.col2.title}</h3>
                                        <ul>
                                            ${separated.col2.content.map(item => `<li>${item.text}</li>`).join('')}
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        `;
                    
                    case 'vertical-list':
                        // Group ordered items with their sub-bullets for export
                        const groups = [];
                        let currentGroup = null;
                        
                        slide.content.forEach(item => {
                            if (item.type === 'ordered') {
                                if (currentGroup) groups.push(currentGroup);
                                currentGroup = { title: item.text, items: [] };
                            } else if (item.type === 'bullet' && currentGroup) {
                                currentGroup.items.push(item.text);
                            }
                        });
                        if (currentGroup) groups.push(currentGroup);
                        
                        return `
                            <div class="slide">
                                <h2>${slide.title}</h2>
                                <ol class="ordered-list">
                                    ${groups.map((group, index) => `
                                        <li>
                                            <div class="main-item">
                                                <div class="item-number">${index + 1}</div>
                                                <span>${group.title}</span>
                                            </div>
                                            ${group.items.length > 0 ? `
                                                <ul class="sub-items">
                                                    ${group.items.map(item => `<li>${item}</li>`).join('')}
                                                </ul>
                                            ` : ''}
                                        </li>
                                    `).join('')}
                                </ol>
                            </div>
                        `;
                    
                    case 'timeline':
                        const timelineContent = slide.content.filter(item => item.type !== 'table');
                        return `
                            <div class="slide">
                                <h2>${slide.title}</h2>
                                <div class="timeline">
                                    ${timelineContent.slice(0, 4).map((item, index) => `
                                        <div class="timeline-item">
                                            <div class="timeline-marker">${index + 1}</div>
                                            <h4>${item.type === 'bullet' ? item.text.split(':')[0] : `Step ${index + 1}`}</h4>
                                            <p>${item.type === 'bullet' && item.text.includes(':') ? item.text.split(':')[1] : item.text}</p>
                                        </div>
                                    `).join('')}
                                </div>
                            </div>
                        `;
                    
                    default:
                        const contentItems = slide.content.filter(item => item.type !== 'table');
                        const tableItems = slide.content.filter(item => item.type === 'table');
                        return `
                            <div class="slide">
                                <h2>${slide.title}</h2>
                                <ul>
                                    ${contentItems.map(item => 
                                        `<li>${item.text}</li>`
                                    ).join('')}
                                </ul>
                                ${tableItems.map(table => `
                                    <div class="table-container">
                                        <table class="standard-table">
                                            <thead>
                                                <tr>
                                                    ${table.headers.map(header => `<th>${header}</th>`).join('')}
                                                </tr>
                                            </thead>
                                            <tbody>
                                                ${table.rows.map(row => `
                                                    <tr>
                                                        ${row.map(cell => `<td>${cell}</td>`).join('')}
                                                    </tr>
                                                `).join('')}
                                            </tbody>
                                        </table>
                                    </div>
                                `).join('')}
                            </div>
                        `;
                }
            }).join('');

            return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Generated Presentation</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    ${css}
</head>
<body>
    ${slidesHTML}
</body>
</html>`;
        }




    </script>
</body>
</html>