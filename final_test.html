<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>最终功能测试</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; }
        .test-panel { background: white; padding: 20px; margin: 20px 0; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .btn { background: #007bff; color: white; border: none; padding: 10px 20px; margin: 5px; border-radius: 5px; cursor: pointer; }
        .btn:hover { background: #0056b3; }
        .btn.success { background: #28a745; }
        .btn.danger { background: #dc3545; }
        .status { padding: 15px; margin: 10px 0; border-radius: 5px; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .test-result { margin: 10px 0; padding: 10px; border-left: 4px solid #007bff; background: #f8f9fa; }
        iframe { width: 100%; height: 600px; border: 1px solid #ddd; border-radius: 5px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 PPT生成器最终功能测试</h1>
        
        <div class="test-panel">
            <h3>📋 测试状态</h3>
            <div id="test-status" class="status info">
                <strong>测试时间：</strong> <span id="test-time"></span><br>
                <strong>浏览器：</strong> <span id="browser-info"></span><br>
                <strong>状态：</strong> 准备开始测试
            </div>
        </div>

        <div class="test-panel">
            <h3>🔧 自动化测试</h3>
            <button class="btn" onclick="runAllTests()">运行所有测试</button>
            <button class="btn" onclick="testBasicFunctionality()">测试基础功能</button>
            <button class="btn" onclick="testGenerateButton()">测试生成按钮</button>
            <button class="btn" onclick="testPreviewSystem()">测试预览系统</button>
            <button class="btn" onclick="clearResults()">清空结果</button>
            
            <div id="test-results"></div>
        </div>

        <div class="test-panel">
            <h3>🖥️ 实时页面预览</h3>
            <p>下面是PPT生成器的实时预览，您可以直接在其中测试功能：</p>
            <iframe src="ppt_generator.html" id="main-iframe"></iframe>
        </div>

        <div class="test-panel">
            <h3>🔗 快速操作</h3>
            <button class="btn success" onclick="openInNewTab()">在新标签页中打开</button>
            <button class="btn" onclick="reloadIframe()">重新加载预览</button>
            <button class="btn danger" onclick="window.location.reload()">刷新测试页面</button>
        </div>
    </div>

    <script>
        // 初始化页面信息
        document.getElementById('test-time').textContent = new Date().toLocaleString();
        document.getElementById('browser-info').textContent = navigator.userAgent.split(' ').slice(-2).join(' ');

        let testResults = document.getElementById('test-results');
        let testCount = 0;

        function updateStatus(message, type = 'info') {
            const statusDiv = document.getElementById('test-status');
            statusDiv.className = `status ${type}`;
            statusDiv.innerHTML = `
                <strong>测试时间：</strong> ${new Date().toLocaleString()}<br>
                <strong>浏览器：</strong> ${navigator.userAgent.split(' ').slice(-2).join(' ')}<br>
                <strong>状态：</strong> ${message}
            `;
        }

        function addTestResult(testName, result, details = '') {
            testCount++;
            const resultClass = result ? 'success' : 'error';
            const resultIcon = result ? '✅' : '❌';
            
            testResults.innerHTML += `
                <div class="test-result">
                    <strong>${resultIcon} 测试 ${testCount}: ${testName}</strong><br>
                    <small>结果: ${result ? '通过' : '失败'}</small>
                    ${details ? `<br><small>详情: ${details}</small>` : ''}
                </div>
            `;
        }

        function clearResults() {
            testResults.innerHTML = '';
            testCount = 0;
            updateStatus('测试结果已清空');
        }

        function testBasicFunctionality() {
            updateStatus('正在测试基础功能...', 'info');
            
            try {
                const iframe = document.getElementById('main-iframe');
                const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                
                // 测试关键元素是否存在
                const generateBtn = iframeDoc.getElementById('generate-btn');
                const outlineInput = iframeDoc.getElementById('outline-input');
                const slidesContainer = iframeDoc.getElementById('slides-container');
                
                addTestResult('生成按钮存在', !!generateBtn);
                addTestResult('输入框存在', !!outlineInput);
                addTestResult('幻灯片容器存在', !!slidesContainer);
                
                // 测试JavaScript函数是否存在
                const iframeWindow = iframe.contentWindow;
                addTestResult('generateSlides函数存在', typeof iframeWindow.generateSlides === 'function');
                addTestResult('parseOutline函数存在', typeof iframeWindow.parseOutline === 'function');
                
                updateStatus('基础功能测试完成', 'success');
                
            } catch (error) {
                addTestResult('基础功能测试', false, error.message);
                updateStatus('基础功能测试失败', 'error');
            }
        }

        function testGenerateButton() {
            updateStatus('正在测试生成按钮...', 'info');
            
            try {
                const iframe = document.getElementById('main-iframe');
                const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                const iframeWindow = iframe.contentWindow;
                
                // 设置测试内容
                const outlineInput = iframeDoc.getElementById('outline-input');
                if (outlineInput) {
                    outlineInput.value = `# 测试标题
## 测试副标题
- 测试项目1
- 测试项目2`;
                    
                    addTestResult('设置测试内容', true);
                    
                    // 模拟点击生成按钮
                    const generateBtn = iframeDoc.getElementById('generate-btn');
                    if (generateBtn) {
                        generateBtn.click();
                        addTestResult('点击生成按钮', true);
                        
                        // 等待一段时间检查结果
                        setTimeout(() => {
                            const slidesContainer = iframeDoc.getElementById('slides-container');
                            const hasContent = slidesContainer && slidesContainer.innerHTML.trim() !== '';
                            addTestResult('生成内容检查', hasContent, hasContent ? '已生成内容' : '未生成内容');
                            
                            updateStatus('生成按钮测试完成', hasContent ? 'success' : 'error');
                        }, 2000);
                        
                    } else {
                        addTestResult('点击生成按钮', false, '按钮不存在');
                        updateStatus('生成按钮测试失败', 'error');
                    }
                } else {
                    addTestResult('设置测试内容', false, '输入框不存在');
                    updateStatus('生成按钮测试失败', 'error');
                }
                
            } catch (error) {
                addTestResult('生成按钮测试', false, error.message);
                updateStatus('生成按钮测试失败', 'error');
            }
        }

        function testPreviewSystem() {
            updateStatus('正在测试预览系统...', 'info');
            
            try {
                const iframe = document.getElementById('main-iframe');
                const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                
                // 检查预览相关元素
                const slidesContainer = iframeDoc.getElementById('slides-container');
                const prevBtn = iframeDoc.getElementById('prev-slide');
                const nextBtn = iframeDoc.getElementById('next-slide');
                const slideIndicator = iframeDoc.getElementById('slide-indicator');
                
                addTestResult('幻灯片容器', !!slidesContainer);
                addTestResult('上一页按钮', !!prevBtn);
                addTestResult('下一页按钮', !!nextBtn);
                addTestResult('幻灯片指示器', !!slideIndicator);
                
                updateStatus('预览系统测试完成', 'success');
                
            } catch (error) {
                addTestResult('预览系统测试', false, error.message);
                updateStatus('预览系统测试失败', 'error');
            }
        }

        function runAllTests() {
            clearResults();
            updateStatus('正在运行所有测试...', 'info');
            
            testBasicFunctionality();
            
            setTimeout(() => {
                testPreviewSystem();
            }, 1000);
            
            setTimeout(() => {
                testGenerateButton();
            }, 2000);
            
            setTimeout(() => {
                updateStatus('所有测试完成', 'success');
            }, 5000);
        }

        function openInNewTab() {
            window.open('ppt_generator.html', '_blank');
        }

        function reloadIframe() {
            document.getElementById('main-iframe').src = 'ppt_generator.html';
            updateStatus('预览已重新加载');
        }

        // 自动运行基础测试
        setTimeout(() => {
            updateStatus('自动运行基础测试...', 'info');
            testBasicFunctionality();
        }, 2000);
    </script>
</body>
</html>
